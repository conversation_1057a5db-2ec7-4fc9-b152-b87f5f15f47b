// This file is generated automatically by Next.js
// Do not edit this file manually
// This file validates that all pages and layouts export the correct types

import type { AppRoutes, LayoutRoutes, ParamMap } from "./routes.js"
import type { ResolvingMetadata, ResolvingViewport } from "next/dist/lib/metadata/types/metadata-interface.js"

type AppPageConfig<Route extends AppRoutes = AppRoutes> = {
  default: React.ComponentType<{ params: Promise<ParamMap[Route]> } & any> | ((props: { params: Promise<ParamMap[Route]> } & any) => React.ReactNode | Promise<React.ReactNode> | never | void | Promise<void>)
  generateStaticParams?: (props: { params: ParamMap[Route] }) => Promise<any[]> | any[]
  generateMetadata?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingMetadata
  ) => Promise<any> | any
  generateViewport?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingViewport
  ) => Promise<any> | any
  metadata?: any
  viewport?: any
}

type LayoutConfig<Route extends LayoutRoutes = LayoutRoutes> = {
  default: React.ComponentType<LayoutProps<Route>> | ((props: LayoutProps<Route>) => React.ReactNode | Promise<React.ReactNode> | never | void | Promise<void>)
  generateStaticParams?: (props: { params: ParamMap[Route] }) => Promise<any[]> | any[]
  generateMetadata?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingMetadata
  ) => Promise<any> | any
  generateViewport?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingViewport
  ) => Promise<any> | any
  metadata?: any
  viewport?: any
}


// Validate ../../src/app/dashboard/agentes/page.tsx
{
  const handler = {} as typeof import("../../src/app/dashboard/agentes/page.js")
  handler satisfies AppPageConfig<"/dashboard/agentes">
}

// Validate ../../src/app/dashboard/campanhas/page.tsx
{
  const handler = {} as typeof import("../../src/app/dashboard/campanhas/page.js")
  handler satisfies AppPageConfig<"/dashboard/campanhas">
}

// Validate ../../src/app/dashboard/chat/page.tsx
{
  const handler = {} as typeof import("../../src/app/dashboard/chat/page.js")
  handler satisfies AppPageConfig<"/dashboard/chat">
}

// Validate ../../src/app/dashboard/configuracoes/page.tsx
{
  const handler = {} as typeof import("../../src/app/dashboard/configuracoes/page.js")
  handler satisfies AppPageConfig<"/dashboard/configuracoes">
}

// Validate ../../src/app/dashboard/page.tsx
{
  const handler = {} as typeof import("../../src/app/dashboard/page.js")
  handler satisfies AppPageConfig<"/dashboard">
}

// Validate ../../src/app/dashboard/relatorios/page.tsx
{
  const handler = {} as typeof import("../../src/app/dashboard/relatorios/page.js")
  handler satisfies AppPageConfig<"/dashboard/relatorios">
}

// Validate ../../src/app/page.tsx
{
  const handler = {} as typeof import("../../src/app/page.js")
  handler satisfies AppPageConfig<"/">
}

// Validate ../../src/app/preco/page.tsx
{
  const handler = {} as typeof import("../../src/app/preco/page.js")
  handler satisfies AppPageConfig<"/preco">
}

// Validate ../../src/app/sucesso/page.tsx
{
  const handler = {} as typeof import("../../src/app/sucesso/page.js")
  handler satisfies AppPageConfig<"/sucesso">
}

// Validate ../../src/app/test-modal/page.tsx
{
  const handler = {} as typeof import("../../src/app/test-modal/page.js")
  handler satisfies AppPageConfig<"/test-modal">
}

// Validate ../../src/app/teste/page.tsx
{
  const handler = {} as typeof import("../../src/app/teste/page.js")
  handler satisfies AppPageConfig<"/teste">
}







// Validate ../../src/app/layout.tsx
{
  const handler = {} as typeof import("../../src/app/layout.js")
  handler satisfies LayoutConfig<"/">
}
