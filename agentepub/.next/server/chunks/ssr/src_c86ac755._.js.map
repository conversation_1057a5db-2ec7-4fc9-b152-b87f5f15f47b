{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent3/agentepub/src/components/auth/AuthModal.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Dialog, DialogPanel, DialogTitle } from '@headlessui/react'\nimport { XMarkIcon } from '@heroicons/react/24/outline'\nimport { useAuth } from '@/contexts/AuthContext'\n\ninterface AuthModalProps {\n  isOpen: boolean\n  onClose: () => void\n  defaultMode?: 'login' | 'signup'\n}\n\nexport default function AuthModal({ isOpen, onClose, defaultMode = 'login' }: AuthModalProps) {\n  const [mode, setMode] = useState<'login' | 'signup'>(defaultMode)\n  const [email, setEmail] = useState('')\n  const [password, setPassword] = useState('')\n  const [fullName, setFullName] = useState('')\n  const [loading, setLoading] = useState(false)\n  const [error, setError] = useState('')\n\n  const { signIn, signUp } = useAuth()\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    setLoading(true)\n    setError('')\n\n    try {\n      let result\n      if (mode === 'login') {\n        result = await signIn(email, password)\n      } else {\n        result = await signUp(email, password, fullName)\n      }\n\n      if (result.error) {\n        setError(result.error.message || 'Ocorreu um erro')\n      } else {\n        onClose()\n        // Reset form\n        setEmail('')\n        setPassword('')\n        setFullName('')\n      }\n    } catch (err) {\n      setError('Ocorreu um erro inesperado')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const resetForm = () => {\n    setEmail('')\n    setPassword('')\n    setFullName('')\n    setError('')\n  }\n\n  const switchMode = () => {\n    setMode(mode === 'login' ? 'signup' : 'login')\n    resetForm()\n  }\n\n  return (\n    <Dialog open={isOpen} onClose={onClose} className=\"relative z-50\">\n      <div className=\"fixed inset-0 bg-black/30\" aria-hidden=\"true\" />\n\n      <div className=\"fixed inset-0 flex items-center justify-center p-4\">\n        <DialogPanel className=\"mx-auto max-w-md w-full bg-white rounded-lg shadow-xl\">\n          <div className=\"flex items-center justify-between p-6 border-b\">\n            <DialogTitle className=\"text-lg font-semibold text-gray-900\">\n              {mode === 'login' ? 'Entrar' : 'Criar Conta'}\n            </DialogTitle>\n            <button\n              onClick={onClose}\n              className=\"text-gray-400 hover:text-gray-600 transition-colors\"\n            >\n              <XMarkIcon className=\"h-6 w-6\" />\n            </button>\n          </div>\n\n          <form onSubmit={handleSubmit} className=\"p-6 space-y-4\">\n            {error && (\n              <div className=\"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md text-sm\">\n                {error}\n              </div>\n            )}\n\n            {mode === 'signup' && (\n              <div>\n                <label htmlFor=\"fullName\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Nome Completo\n                </label>\n                <input\n                  id=\"fullName\"\n                  type=\"text\"\n                  value={fullName}\n                  onChange={(e) => setFullName(e.target.value)}\n                  required\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n                  placeholder=\"Seu nome completo\"\n                />\n              </div>\n            )}\n\n            <div>\n              <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                E-mail\n              </label>\n              <input\n                id=\"email\"\n                type=\"email\"\n                value={email}\n                onChange={(e) => setEmail(e.target.value)}\n                required\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n                placeholder=\"<EMAIL>\"\n              />\n            </div>\n\n            <div>\n              <label htmlFor=\"password\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                Senha\n              </label>\n              <input\n                id=\"password\"\n                type=\"password\"\n                value={password}\n                onChange={(e) => setPassword(e.target.value)}\n                required\n                minLength={6}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n                placeholder=\"Mínimo 6 caracteres\"\n              />\n            </div>\n\n            <button\n              type=\"submit\"\n              disabled={loading}\n              className={`w-full bg-blue-600 text-white py-3 px-4 rounded-md font-medium transition-colors ${loading\n                ? \"opacity-50 cursor-not-allowed\"\n                : \"hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\"\n                }`}\n            >\n              {loading ? 'Carregando...' : mode === 'login' ? 'Entrar' : 'Criar Conta'}\n            </button>\n\n            <div className=\"text-center\">\n              <button\n                type=\"button\"\n                onClick={switchMode}\n                className=\"text-sm text-blue-600 hover:text-blue-700 transition-colors\"\n              >\n                {mode === 'login'\n                  ? 'Não tem conta? Criar uma agora'\n                  : 'Já tem conta? Fazer login'\n                }\n              </button>\n            </div>\n          </form>\n        </DialogPanel>\n      </div>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAae,SAAS,UAAU,EAAE,MAAM,EAAE,OAAO,EAAE,cAAc,OAAO,EAAkB;IAC1F,MAAM,CAAC,MAAM,QAAQ,GAAG,IAAA,iNAAQ,EAAqB;IACrD,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,iNAAQ,EAAC;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,iNAAQ,EAAC;IACzC,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,iNAAQ,EAAC;IACzC,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,iNAAQ,EAAC;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,iNAAQ,EAAC;IAEnC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,IAAA,0IAAO;IAElC,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,WAAW;QACX,SAAS;QAET,IAAI;YACF,IAAI;YACJ,IAAI,SAAS,SAAS;gBACpB,SAAS,MAAM,OAAO,OAAO;YAC/B,OAAO;gBACL,SAAS,MAAM,OAAO,OAAO,UAAU;YACzC;YAEA,IAAI,OAAO,KAAK,EAAE;gBAChB,SAAS,OAAO,KAAK,CAAC,OAAO,IAAI;YACnC,OAAO;gBACL;gBACA,aAAa;gBACb,SAAS;gBACT,YAAY;gBACZ,YAAY;YACd;QACF,EAAE,OAAO,KAAK;YACZ,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,YAAY;QAChB,SAAS;QACT,YAAY;QACZ,YAAY;QACZ,SAAS;IACX;IAEA,MAAM,aAAa;QACjB,QAAQ,SAAS,UAAU,WAAW;QACtC;IACF;IAEA,qBACE,8OAAC,yLAAM;QAAC,MAAM;QAAQ,SAAS;QAAS,WAAU;;0BAChD,8OAAC;gBAAI,WAAU;gBAA4B,eAAY;;;;;;0BAEvD,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,8LAAW;oBAAC,WAAU;;sCACrB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,8LAAW;oCAAC,WAAU;8CACpB,SAAS,UAAU,WAAW;;;;;;8CAEjC,8OAAC;oCACC,SAAS;oCACT,WAAU;8CAEV,cAAA,8OAAC,8NAAS;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAIzB,8OAAC;4BAAK,UAAU;4BAAc,WAAU;;gCACrC,uBACC,8OAAC;oCAAI,WAAU;8CACZ;;;;;;gCAIJ,SAAS,0BACR,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAW,WAAU;sDAA+C;;;;;;sDAGnF,8OAAC;4CACC,IAAG;4CACH,MAAK;4CACL,OAAO;4CACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;4CAC3C,QAAQ;4CACR,WAAU;4CACV,aAAY;;;;;;;;;;;;8CAKlB,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAQ,WAAU;sDAA+C;;;;;;sDAGhF,8OAAC;4CACC,IAAG;4CACH,MAAK;4CACL,OAAO;4CACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;4CACxC,QAAQ;4CACR,WAAU;4CACV,aAAY;;;;;;;;;;;;8CAIhB,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAW,WAAU;sDAA+C;;;;;;sDAGnF,8OAAC;4CACC,IAAG;4CACH,MAAK;4CACL,OAAO;4CACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;4CAC3C,QAAQ;4CACR,WAAW;4CACX,WAAU;4CACV,aAAY;;;;;;;;;;;;8CAIhB,8OAAC;oCACC,MAAK;oCACL,UAAU;oCACV,WAAW,CAAC,iFAAiF,EAAE,UAC3F,kCACA,6FACA;8CAEH,UAAU,kBAAkB,SAAS,UAAU,WAAW;;;;;;8CAG7D,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,MAAK;wCACL,SAAS;wCACT,WAAU;kDAET,SAAS,UACN,mCACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASpB", "debugId": null}}, {"offset": {"line": 271, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent3/agentepub/src/app/test-modal/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport AuthModal from '@/components/auth/AuthModal'\nimport SimpleAuthModal from '@/components/auth/SimpleAuthModal'\n\nexport default function TestModalPage() {\n  const [isOpen, setIsOpen] = useState(false)\n\n  return (\n    <div className=\"min-h-screen bg-gray-100 flex items-center justify-center\">\n      <div className=\"bg-white p-8 rounded-lg shadow-lg\">\n        <h1 className=\"text-2xl font-bold mb-4\">Teste do Modal de Autenticação</h1>\n        <button\n          onClick={() => setIsOpen(true)}\n          className=\"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700\"\n        >\n          <PERSON><PERSON><PERSON> de <PERSON>\n        </button>\n\n        <AuthModal\n          isOpen={isOpen}\n          onClose={() => setIsOpen(false)}\n        />\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;AAMe,SAAS;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,IAAA,iNAAQ,EAAC;IAErC,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAA0B;;;;;;8BACxC,8OAAC;oBACC,SAAS,IAAM,UAAU;oBACzB,WAAU;8BACX;;;;;;8BAID,8OAAC,kJAAS;oBACR,QAAQ;oBACR,SAAS,IAAM,UAAU;;;;;;;;;;;;;;;;;AAKnC", "debugId": null}}]}