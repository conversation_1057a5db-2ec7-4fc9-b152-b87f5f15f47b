{"version": 3, "sources": [], "sections": [{"offset": {"line": 22, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent3/agentepub/src/components/auth/ProtectedRoute.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { useAuth } from '@/contexts/AuthContext'\n\ninterface ProtectedRouteProps {\n  children: React.ReactNode\n  redirectTo?: string\n}\n\nexport default function ProtectedRoute({ children, redirectTo = '/' }: ProtectedRouteProps) {\n  const { user, loading } = useAuth()\n  const router = useRouter()\n\n  useEffect(() => {\n    if (!loading && !user) {\n      router.push(redirectTo)\n    }\n  }, [user, loading, router, redirectTo])\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600\"></div>\n      </div>\n    )\n  }\n\n  if (!user) {\n    return null\n  }\n\n  return <>{children}</>\n}\n\n// Loading component for better UX\nexport function LoadingSpinner({ size = 'md' }: { size?: 'sm' | 'md' | 'lg' }) {\n  const sizeClasses = {\n    sm: 'h-4 w-4',\n    md: 'h-8 w-8',\n    lg: 'h-12 w-12'\n  }\n\n  return (\n    <div className=\"flex items-center justify-center\">\n      <div className={`animate-spin rounded-full border-b-2 border-primary-600 ${sizeClasses[size]}`}></div>\n    </div>\n  )\n}\n\n// Auth guard hook for pages\nexport function useAuthGuard(redirectTo = '/') {\n  const { user, loading } = useAuth()\n  const router = useRouter()\n\n  useEffect(() => {\n    if (!loading && !user) {\n      router.push(redirectTo)\n    }\n  }, [user, loading, router, redirectTo])\n\n  return { user, loading, isAuthenticated: !!user }\n}\n"], "names": [], "mappings": ";;;;;;;;;AAEA;AACA;AACA;AAJA;;;;;AAWe,SAAS,eAAe,EAAE,QAAQ,EAAE,aAAa,GAAG,EAAuB;IACxF,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,IAAA,0IAAO;IACjC,MAAM,SAAS,IAAA,+IAAS;IAExB,IAAA,kNAAS,EAAC;QACR,IAAI,CAAC,WAAW,CAAC,MAAM;YACrB,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;QAAM;QAAS;QAAQ;KAAW;IAEtC,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,qBAAO;kBAAG;;AACZ;AAGO,SAAS,eAAe,EAAE,OAAO,IAAI,EAAiC;IAC3E,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAW,CAAC,wDAAwD,EAAE,WAAW,CAAC,KAAK,EAAE;;;;;;;;;;;AAGpG;AAGO,SAAS,aAAa,aAAa,GAAG;IAC3C,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,IAAA,0IAAO;IACjC,MAAM,SAAS,IAAA,+IAAS;IAExB,IAAA,kNAAS,EAAC;QACR,IAAI,CAAC,WAAW,CAAC,MAAM;YACrB,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;QAAM;QAAS;QAAQ;KAAW;IAEtC,OAAO;QAAE;QAAM;QAAS,iBAAiB,CAAC,CAAC;IAAK;AAClD", "debugId": null}}, {"offset": {"line": 119, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent3/agentepub/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatDate(date: string | Date) {\n  return new Intl.DateTimeFormat('pt-BR', {\n    day: '2-digit',\n    month: '2-digit',\n    year: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit',\n  }).format(new Date(date))\n}\n\nexport function formatCurrency(amount: number) {\n  return new Intl.NumberFormat('pt-BR', {\n    style: 'currency',\n    currency: 'BRL',\n  }).format(amount)\n}\n\nexport function getAreaDisplayName(area: string) {\n  const areaNames = {\n    atendimento: 'Atendimento',\n    planejamento: 'Planejamento',\n    midia: 'Mídia'\n  }\n  return areaNames[area as keyof typeof areaNames] || area\n}\n\nexport function generateSlug(text: string) {\n  return text\n    .toLowerCase()\n    .normalize('NFD')\n    .replace(/[\\u0300-\\u036f]/g, '')\n    .replace(/[^a-z0-9\\s-]/g, '')\n    .replace(/\\s+/g, '-')\n    .replace(/-+/g, '-')\n    .trim()\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,IAAA,sKAAO,EAAC,IAAA,6IAAI,EAAC;AACtB;AAEO,SAAS,WAAW,IAAmB;IAC5C,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,KAAK;QACL,OAAO;QACP,MAAM;QACN,MAAM;QACN,QAAQ;IACV,GAAG,MAAM,CAAC,IAAI,KAAK;AACrB;AAEO,SAAS,eAAe,MAAc;IAC3C,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;IACZ,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,mBAAmB,IAAY;IAC7C,MAAM,YAAY;QAChB,aAAa;QACb,cAAc;QACd,OAAO;IACT;IACA,OAAO,SAAS,CAAC,KAA+B,IAAI;AACtD;AAEO,SAAS,aAAa,IAAY;IACvC,OAAO,KACJ,WAAW,GACX,SAAS,CAAC,OACV,OAAO,CAAC,oBAAoB,IAC5B,OAAO,CAAC,iBAAiB,IACzB,OAAO,CAAC,QAAQ,KAChB,OAAO,CAAC,OAAO,KACf,IAAI;AACT", "debugId": null}}, {"offset": {"line": 168, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent3/agentepub/src/components/dashboard/DashboardLayout.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { cn } from '@/lib/utils'\nimport {\n  Bars3Icon,\n  XMarkIcon,\n  HomeIcon,\n  FolderIcon,\n  UserGroupIcon,\n  DocumentChartBarIcon,\n  ChatBubbleLeftRightIcon,\n  Cog6ToothIcon,\n  ArrowRightOnRectangleIcon\n} from '@heroicons/react/24/outline'\n\nconst navigation = [\n  { name: 'Dashboard', href: '/dashboard', icon: HomeIcon },\n  { name: 'Campanhas', href: '/dashboard/campanhas', icon: FolderIcon },\n  { name: 'Agent<PERSON>', href: '/dashboard/agentes', icon: UserGroupIcon },\n  { name: '<PERSON><PERSON><PERSON><PERSON><PERSON>', href: '/dashboard/relatorios', icon: DocumentChartBarIcon },\n  { name: 'Chat', href: '/dashboard/chat', icon: ChatBubbleLeftRightIcon },\n  { name: 'Configura<PERSON>õ<PERSON>', href: '/dashboard/configuracoes', icon: Cog6ToothIcon },\n]\n\ninterface DashboardLayoutProps {\n  children: React.ReactNode\n}\n\nexport default function DashboardLayout({ children }: DashboardLayoutProps) {\n  const [sidebarOpen, setSidebarOpen] = useState(false)\n  const pathname = usePathname()\n  const { user, profile, signOut } = useAuth()\n\n  const handleSignOut = async () => {\n    await signOut()\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Mobile sidebar */}\n      {sidebarOpen && (\n        <div className=\"fixed inset-0 z-50 lg:hidden\">\n          <div className=\"fixed inset-0 bg-gray-600 bg-opacity-75\" onClick={() => setSidebarOpen(false)} />\n          <div className=\"fixed inset-y-0 left-0 flex w-full max-w-xs flex-col bg-white shadow-xl\">\n            <div className=\"flex h-16 items-center justify-between px-6 border-b border-gray-200\">\n              <Link href=\"/dashboard\" className=\"text-xl font-bold text-primary-600\">\n                AgentePub\n              </Link>\n              <button\n                onClick={() => setSidebarOpen(false)}\n                className=\"text-gray-400 hover:text-gray-600\"\n              >\n                <XMarkIcon className=\"h-6 w-6\" />\n              </button>\n            </div>\n            \n            <nav className=\"flex-1 px-6 py-6\">\n              <ul className=\"space-y-2\">\n                {navigation.map((item) => (\n                  <li key={item.name}>\n                    <Link\n                      href={item.href}\n                      className={cn(\n                        'flex items-center gap-3 px-3 py-2 rounded-md text-sm font-medium transition-colors',\n                        pathname === item.href\n                          ? 'bg-primary-100 text-primary-700'\n                          : 'text-gray-700 hover:bg-gray-100'\n                      )}\n                      onClick={() => setSidebarOpen(false)}\n                    >\n                      <item.icon className=\"h-5 w-5\" />\n                      {item.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </nav>\n\n            <div className=\"border-t border-gray-200 p-6\">\n              <div className=\"flex items-center gap-3 mb-4\">\n                <div className=\"h-8 w-8 bg-primary-100 rounded-full flex items-center justify-center\">\n                  <span className=\"text-sm font-medium text-primary-700\">\n                    {profile?.full_name?.charAt(0) || user?.email?.charAt(0) || 'U'}\n                  </span>\n                </div>\n                <div className=\"flex-1 min-w-0\">\n                  <p className=\"text-sm font-medium text-gray-900 truncate\">\n                    {profile?.full_name || user?.email}\n                  </p>\n                  {profile?.area && (\n                    <p className=\"text-xs text-gray-500 capitalize\">{profile.area}</p>\n                  )}\n                </div>\n              </div>\n              <button\n                onClick={handleSignOut}\n                className=\"flex items-center gap-2 text-sm text-gray-700 hover:text-gray-900 transition-colors\"\n              >\n                <ArrowRightOnRectangleIcon className=\"h-4 w-4\" />\n                Sair\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Desktop sidebar */}\n      <div className=\"hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col\">\n        <div className=\"flex flex-col flex-1 bg-white border-r border-gray-200\">\n          <div className=\"flex h-16 items-center px-6 border-b border-gray-200\">\n            <Link href=\"/dashboard\" className=\"text-xl font-bold text-primary-600\">\n              AgentePub\n            </Link>\n          </div>\n          \n          <nav className=\"flex-1 px-6 py-6\">\n            <ul className=\"space-y-2\">\n              {navigation.map((item) => (\n                <li key={item.name}>\n                  <Link\n                    href={item.href}\n                    className={cn(\n                      'flex items-center gap-3 px-3 py-2 rounded-md text-sm font-medium transition-colors',\n                      pathname === item.href\n                        ? 'bg-primary-100 text-primary-700'\n                        : 'text-gray-700 hover:bg-gray-100'\n                    )}\n                  >\n                    <item.icon className=\"h-5 w-5\" />\n                    {item.name}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </nav>\n\n          <div className=\"border-t border-gray-200 p-6\">\n            <div className=\"flex items-center gap-3 mb-4\">\n              <div className=\"h-8 w-8 bg-primary-100 rounded-full flex items-center justify-center\">\n                <span className=\"text-sm font-medium text-primary-700\">\n                  {profile?.full_name?.charAt(0) || user?.email?.charAt(0) || 'U'}\n                </span>\n              </div>\n              <div className=\"flex-1 min-w-0\">\n                <p className=\"text-sm font-medium text-gray-900 truncate\">\n                  {profile?.full_name || user?.email}\n                </p>\n                {profile?.area && (\n                  <p className=\"text-xs text-gray-500 capitalize\">{profile.area}</p>\n                )}\n              </div>\n            </div>\n            <button\n              onClick={handleSignOut}\n              className=\"flex items-center gap-2 text-sm text-gray-700 hover:text-gray-900 transition-colors\"\n            >\n              <ArrowRightOnRectangleIcon className=\"h-4 w-4\" />\n              Sair\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Main content */}\n      <div className=\"lg:pl-64\">\n        {/* Top bar */}\n        <div className=\"sticky top-0 z-40 bg-white border-b border-gray-200 px-6 py-4\">\n          <div className=\"flex items-center justify-between\">\n            <button\n              onClick={() => setSidebarOpen(true)}\n              className=\"lg:hidden text-gray-500 hover:text-gray-700\"\n            >\n              <Bars3Icon className=\"h-6 w-6\" />\n            </button>\n            \n            <div className=\"flex items-center gap-4\">\n              <div className=\"text-sm text-gray-500\">\n                {new Date().toLocaleDateString('pt-BR', { \n                  weekday: 'long', \n                  year: 'numeric', \n                  month: 'long', \n                  day: 'numeric' \n                })}\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Page content */}\n        <main className=\"px-6 py-8\">\n          {children}\n        </main>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAPA;;;;;;;;AAmBA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAa,MAAM;QAAc,MAAM,2NAAQ;IAAC;IACxD;QAAE,MAAM;QAAa,MAAM;QAAwB,MAAM,iOAAU;IAAC;IACpE;QAAE,MAAM;QAAW,MAAM;QAAsB,MAAM,0OAAa;IAAC;IACnE;QAAE,MAAM;QAAc,MAAM;QAAyB,MAAM,+PAAoB;IAAC;IAChF;QAAE,MAAM;QAAQ,MAAM;QAAmB,MAAM,wQAAuB;IAAC;IACvE;QAAE,MAAM;QAAiB,MAAM;QAA4B,MAAM,0OAAa;IAAC;CAChF;AAMc,SAAS,gBAAgB,EAAE,QAAQ,EAAwB;IACxE,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,iNAAQ,EAAC;IAC/C,MAAM,WAAW,IAAA,iJAAW;IAC5B,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,IAAA,0IAAO;IAE1C,MAAM,gBAAgB;QACpB,MAAM;IACR;IAEA,qBACE,8OAAC;QAAI,WAAU;;YAEZ,6BACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;wBAA0C,SAAS,IAAM,eAAe;;;;;;kCACvF,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,uKAAI;wCAAC,MAAK;wCAAa,WAAU;kDAAqC;;;;;;kDAGvE,8OAAC;wCACC,SAAS,IAAM,eAAe;wCAC9B,WAAU;kDAEV,cAAA,8OAAC,8NAAS;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAIzB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAG,WAAU;8CACX,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC;sDACC,cAAA,8OAAC,uKAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAW,IAAA,yHAAE,EACX,sFACA,aAAa,KAAK,IAAI,GAClB,oCACA;gDAEN,SAAS,IAAM,eAAe;;kEAE9B,8OAAC,KAAK,IAAI;wDAAC,WAAU;;;;;;oDACpB,KAAK,IAAI;;;;;;;2CAZL,KAAK,IAAI;;;;;;;;;;;;;;;0CAmBxB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;8DACb,SAAS,WAAW,OAAO,MAAM,MAAM,OAAO,OAAO,MAAM;;;;;;;;;;;0DAGhE,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEACV,SAAS,aAAa,MAAM;;;;;;oDAE9B,SAAS,sBACR,8OAAC;wDAAE,WAAU;kEAAoC,QAAQ,IAAI;;;;;;;;;;;;;;;;;;kDAInE,8OAAC;wCACC,SAAS;wCACT,WAAU;;0DAEV,8OAAC,8QAAyB;gDAAC,WAAU;;;;;;4CAAY;;;;;;;;;;;;;;;;;;;;;;;;;0BAS3D,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,uKAAI;gCAAC,MAAK;gCAAa,WAAU;0CAAqC;;;;;;;;;;;sCAKzE,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAG,WAAU;0CACX,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC;kDACC,cAAA,8OAAC,uKAAI;4CACH,MAAM,KAAK,IAAI;4CACf,WAAW,IAAA,yHAAE,EACX,sFACA,aAAa,KAAK,IAAI,GAClB,oCACA;;8DAGN,8OAAC,KAAK,IAAI;oDAAC,WAAU;;;;;;gDACpB,KAAK,IAAI;;;;;;;uCAXL,KAAK,IAAI;;;;;;;;;;;;;;;sCAkBxB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DACb,SAAS,WAAW,OAAO,MAAM,MAAM,OAAO,OAAO,MAAM;;;;;;;;;;;sDAGhE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DACV,SAAS,aAAa,MAAM;;;;;;gDAE9B,SAAS,sBACR,8OAAC;oDAAE,WAAU;8DAAoC,QAAQ,IAAI;;;;;;;;;;;;;;;;;;8CAInE,8OAAC;oCACC,SAAS;oCACT,WAAU;;sDAEV,8OAAC,8QAAyB;4CAAC,WAAU;;;;;;wCAAY;;;;;;;;;;;;;;;;;;;;;;;;0BAQzD,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAS,IAAM,eAAe;oCAC9B,WAAU;8CAEV,cAAA,8OAAC,8NAAS;wCAAC,WAAU;;;;;;;;;;;8CAGvB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACZ,IAAI,OAAO,kBAAkB,CAAC,SAAS;4CACtC,SAAS;4CACT,MAAM;4CACN,OAAO;4CACP,KAAK;wCACP;;;;;;;;;;;;;;;;;;;;;;kCAOR,8OAAC;wBAAK,WAAU;kCACb;;;;;;;;;;;;;;;;;;AAKX", "debugId": null}}, {"offset": {"line": 636, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent3/agentepub/src/app/dashboard/chat/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect, useRef } from 'react'\nimport ProtectedRoute from '@/components/auth/ProtectedRoute'\nimport DashboardLayout from '@/components/dashboard/DashboardLayout'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { \n  PaperAirplaneIcon,\n  UserCircleIcon,\n  EllipsisVerticalIcon\n} from '@heroicons/react/24/outline'\n\n// Mock messages - in real app this would come from Supabase Realtime\nconst mockMessages = [\n  {\n    id: '1',\n    user_id: 'user1',\n    user_name: '<PERSON>',\n    user_area: 'atendimento',\n    message: '<PERSON><PERSON>oa<PERSON>, o cliente da campanha Black Friday aprovou o cronograma! 🎉',\n    created_at: '2024-01-15T10:30:00Z'\n  },\n  {\n    id: '2',\n    user_id: 'user2',\n    user_name: '<PERSON>',\n    user_area: 'planejamento',\n    message: 'Perfeito! J<PERSON> posso começar a produção dos materiais então.',\n    created_at: '2024-01-15T10:32:00Z'\n  },\n  {\n    id: '3',\n    user_id: 'user3',\n    user_name: '<PERSON>',\n    user_area: 'midia',\n    message: '<PERSON><PERSON> configurar as campanhas no Meta Ads hoje à tarde.',\n    created_at: '2024-01-15T10:35:00Z'\n  },\n  {\n    id: '4',\n    user_id: 'user1',\n    user_name: 'Ana Silva',\n    user_area: 'atendimento',\n    message: 'Ótimo! Lembrem-se que o prazo para entrega dos criativos é quinta-feira.',\n    created_at: '2024-01-15T10:40:00Z'\n  },\n  {\n    id: '5',\n    user_id: 'user4',\n    user_name: 'João Oliveira',\n    user_area: 'planejamento',\n    message: 'Pessoal, acabei de processar um novo briefing com o agente de atendimento. Muito mais rápido que antes! 🚀',\n    created_at: '2024-01-15T11:15:00Z'\n  }\n]\n\nconst teamMembers = [\n  { id: 'user1', name: 'Ana Silva', area: 'atendimento', online: true },\n  { id: 'user2', name: 'Carlos Santos', area: 'planejamento', online: true },\n  { id: 'user3', name: 'Maria Costa', area: 'midia', online: false },\n  { id: 'user4', name: 'João Oliveira', area: 'planejamento', online: true }\n]\n\nexport default function ChatPage() {\n  const [messages, setMessages] = useState(mockMessages)\n  const [newMessage, setNewMessage] = useState('')\n  const [isTyping, setIsTyping] = useState(false)\n  const messagesEndRef = useRef<HTMLDivElement>(null)\n  const { user, profile } = useAuth()\n\n  const scrollToBottom = () => {\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })\n  }\n\n  useEffect(() => {\n    scrollToBottom()\n  }, [messages])\n\n  const handleSendMessage = (e: React.FormEvent) => {\n    e.preventDefault()\n    if (!newMessage.trim() || !user || !profile) return\n\n    const message = {\n      id: Date.now().toString(),\n      user_id: user.id,\n      user_name: profile.full_name || user.email || 'Usuário',\n      user_area: profile.area || 'geral',\n      message: newMessage.trim(),\n      created_at: new Date().toISOString()\n    }\n\n    setMessages(prev => [...prev, message])\n    setNewMessage('')\n\n    // In real app, this would send to Supabase Realtime\n    console.log('Sending message:', message)\n  }\n\n  const formatTime = (dateString: string) => {\n    return new Date(dateString).toLocaleTimeString('pt-BR', {\n      hour: '2-digit',\n      minute: '2-digit'\n    })\n  }\n\n  const getAreaColor = (area: string) => {\n    switch (area) {\n      case 'atendimento':\n        return 'bg-blue-100 text-blue-800'\n      case 'planejamento':\n        return 'bg-green-100 text-green-800'\n      case 'midia':\n        return 'bg-purple-100 text-purple-800'\n      default:\n        return 'bg-gray-100 text-gray-800'\n    }\n  }\n\n  const getAreaName = (area: string) => {\n    switch (area) {\n      case 'atendimento':\n        return 'Atendimento'\n      case 'planejamento':\n        return 'Planejamento'\n      case 'midia':\n        return 'Mídia'\n      default:\n        return 'Geral'\n    }\n  }\n\n  return (\n    <ProtectedRoute>\n      <DashboardLayout>\n        <div className=\"h-[calc(100vh-12rem)] flex bg-white rounded-lg shadow\">\n          {/* Sidebar - Team Members */}\n          <div className=\"w-64 border-r border-gray-200 flex flex-col\">\n            <div className=\"p-4 border-b border-gray-200\">\n              <h2 className=\"font-semibold text-gray-900\">Equipe Online</h2>\n              <p className=\"text-sm text-gray-600\">{teamMembers.filter(m => m.online).length} membros online</p>\n            </div>\n            \n            <div className=\"flex-1 overflow-y-auto p-4\">\n              <div className=\"space-y-3\">\n                {teamMembers.map((member) => (\n                  <div key={member.id} className=\"flex items-center gap-3\">\n                    <div className=\"relative\">\n                      <UserCircleIcon className=\"h-8 w-8 text-gray-400\" />\n                      <div className={`absolute -bottom-0.5 -right-0.5 w-3 h-3 rounded-full border-2 border-white ${\n                        member.online ? 'bg-green-400' : 'bg-gray-300'\n                      }`} />\n                    </div>\n                    <div className=\"flex-1 min-w-0\">\n                      <p className=\"text-sm font-medium text-gray-900 truncate\">{member.name}</p>\n                      <p className={`text-xs px-2 py-0.5 rounded-full inline-block ${getAreaColor(member.area)}`}>\n                        {getAreaName(member.area)}\n                      </p>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n          </div>\n\n          {/* Chat Area */}\n          <div className=\"flex-1 flex flex-col\">\n            {/* Chat Header */}\n            <div className=\"p-4 border-b border-gray-200 flex items-center justify-between\">\n              <div>\n                <h1 className=\"font-semibold text-gray-900\">Chat da Equipe</h1>\n                <p className=\"text-sm text-gray-600\">Comunicação interna em tempo real</p>\n              </div>\n              <button className=\"text-gray-400 hover:text-gray-600\">\n                <EllipsisVerticalIcon className=\"h-5 w-5\" />\n              </button>\n            </div>\n\n            {/* Messages */}\n            <div className=\"flex-1 overflow-y-auto p-4 space-y-4\">\n              {messages.map((message) => {\n                const isOwnMessage = message.user_id === user?.id\n                \n                return (\n                  <div key={message.id} className={`flex ${isOwnMessage ? 'justify-end' : 'justify-start'}`}>\n                    <div className={`max-w-xs lg:max-w-md ${isOwnMessage ? 'order-2' : 'order-1'}`}>\n                      {!isOwnMessage && (\n                        <div className=\"flex items-center gap-2 mb-1\">\n                          <span className=\"text-sm font-medium text-gray-900\">{message.user_name}</span>\n                          <span className={`text-xs px-2 py-0.5 rounded-full ${getAreaColor(message.user_area)}`}>\n                            {getAreaName(message.user_area)}\n                          </span>\n                        </div>\n                      )}\n                      \n                      <div className={`rounded-lg px-4 py-2 ${\n                        isOwnMessage \n                          ? 'bg-primary-600 text-white' \n                          : 'bg-gray-100 text-gray-900'\n                      }`}>\n                        <p className=\"text-sm\">{message.message}</p>\n                      </div>\n                      \n                      <p className={`text-xs text-gray-500 mt-1 ${isOwnMessage ? 'text-right' : 'text-left'}`}>\n                        {formatTime(message.created_at)}\n                      </p>\n                    </div>\n                  </div>\n                )\n              })}\n              \n              {isTyping && (\n                <div className=\"flex justify-start\">\n                  <div className=\"bg-gray-100 rounded-lg px-4 py-2\">\n                    <div className=\"flex space-x-1\">\n                      <div className=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\"></div>\n                      <div className=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\" style={{ animationDelay: '0.1s' }}></div>\n                      <div className=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\" style={{ animationDelay: '0.2s' }}></div>\n                    </div>\n                  </div>\n                </div>\n              )}\n              \n              <div ref={messagesEndRef} />\n            </div>\n\n            {/* Message Input */}\n            <div className=\"p-4 border-t border-gray-200\">\n              <form onSubmit={handleSendMessage} className=\"flex gap-2\">\n                <input\n                  type=\"text\"\n                  value={newMessage}\n                  onChange={(e) => setNewMessage(e.target.value)}\n                  placeholder=\"Digite sua mensagem...\"\n                  className=\"flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n                />\n                <button\n                  type=\"submit\"\n                  disabled={!newMessage.trim()}\n                  className=\"bg-primary-600 text-white p-2 rounded-lg hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n                >\n                  <PaperAirplaneIcon className=\"h-5 w-5\" />\n                </button>\n              </form>\n            </div>\n          </div>\n        </div>\n      </DashboardLayout>\n    </ProtectedRoute>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AANA;;;;;;;AAYA,qEAAqE;AACrE,MAAM,eAAe;IACnB;QACE,IAAI;QACJ,SAAS;QACT,WAAW;QACX,WAAW;QACX,SAAS;QACT,YAAY;IACd;IACA;QACE,IAAI;QACJ,SAAS;QACT,WAAW;QACX,WAAW;QACX,SAAS;QACT,YAAY;IACd;IACA;QACE,IAAI;QACJ,SAAS;QACT,WAAW;QACX,WAAW;QACX,SAAS;QACT,YAAY;IACd;IACA;QACE,IAAI;QACJ,SAAS;QACT,WAAW;QACX,WAAW;QACX,SAAS;QACT,YAAY;IACd;IACA;QACE,IAAI;QACJ,SAAS;QACT,WAAW;QACX,WAAW;QACX,SAAS;QACT,YAAY;IACd;CACD;AAED,MAAM,cAAc;IAClB;QAAE,IAAI;QAAS,MAAM;QAAa,MAAM;QAAe,QAAQ;IAAK;IACpE;QAAE,IAAI;QAAS,MAAM;QAAiB,MAAM;QAAgB,QAAQ;IAAK;IACzE;QAAE,IAAI;QAAS,MAAM;QAAe,MAAM;QAAS,QAAQ;IAAM;IACjE;QAAE,IAAI;QAAS,MAAM;QAAiB,MAAM;QAAgB,QAAQ;IAAK;CAC1E;AAEc,SAAS;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,iNAAQ,EAAC;IACzC,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,iNAAQ,EAAC;IAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,iNAAQ,EAAC;IACzC,MAAM,iBAAiB,IAAA,+MAAM,EAAiB;IAC9C,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,IAAA,0IAAO;IAEjC,MAAM,iBAAiB;QACrB,eAAe,OAAO,EAAE,eAAe;YAAE,UAAU;QAAS;IAC9D;IAEA,IAAA,kNAAS,EAAC;QACR;IACF,GAAG;QAAC;KAAS;IAEb,MAAM,oBAAoB,CAAC;QACzB,EAAE,cAAc;QAChB,IAAI,CAAC,WAAW,IAAI,MAAM,CAAC,QAAQ,CAAC,SAAS;QAE7C,MAAM,UAAU;YACd,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,SAAS,KAAK,EAAE;YAChB,WAAW,QAAQ,SAAS,IAAI,KAAK,KAAK,IAAI;YAC9C,WAAW,QAAQ,IAAI,IAAI;YAC3B,SAAS,WAAW,IAAI;YACxB,YAAY,IAAI,OAAO,WAAW;QACpC;QAEA,YAAY,CAAA,OAAQ;mBAAI;gBAAM;aAAQ;QACtC,cAAc;QAEd,oDAAoD;QACpD,QAAQ,GAAG,CAAC,oBAAoB;IAClC;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,MAAM;YACN,QAAQ;QACV;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,8OAAC,uJAAc;kBACb,cAAA,8OAAC,6JAAe;sBACd,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA8B;;;;;;kDAC5C,8OAAC;wCAAE,WAAU;;4CAAyB,YAAY,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,EAAE,MAAM;4CAAC;;;;;;;;;;;;;0CAGjF,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CACZ,YAAY,GAAG,CAAC,CAAC,uBAChB,8OAAC;4CAAoB,WAAU;;8DAC7B,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,6OAAc;4DAAC,WAAU;;;;;;sEAC1B,8OAAC;4DAAI,WAAW,CAAC,2EAA2E,EAC1F,OAAO,MAAM,GAAG,iBAAiB,eACjC;;;;;;;;;;;;8DAEJ,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;sEAA8C,OAAO,IAAI;;;;;;sEACtE,8OAAC;4DAAE,WAAW,CAAC,8CAA8C,EAAE,aAAa,OAAO,IAAI,GAAG;sEACvF,YAAY,OAAO,IAAI;;;;;;;;;;;;;2CAVpB,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;kCAoB3B,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAA8B;;;;;;0DAC5C,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;kDAEvC,8OAAC;wCAAO,WAAU;kDAChB,cAAA,8OAAC,+PAAoB;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAKpC,8OAAC;gCAAI,WAAU;;oCACZ,SAAS,GAAG,CAAC,CAAC;wCACb,MAAM,eAAe,QAAQ,OAAO,KAAK,MAAM;wCAE/C,qBACE,8OAAC;4CAAqB,WAAW,CAAC,KAAK,EAAE,eAAe,gBAAgB,iBAAiB;sDACvF,cAAA,8OAAC;gDAAI,WAAW,CAAC,qBAAqB,EAAE,eAAe,YAAY,WAAW;;oDAC3E,CAAC,8BACA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EAAqC,QAAQ,SAAS;;;;;;0EACtE,8OAAC;gEAAK,WAAW,CAAC,iCAAiC,EAAE,aAAa,QAAQ,SAAS,GAAG;0EACnF,YAAY,QAAQ,SAAS;;;;;;;;;;;;kEAKpC,8OAAC;wDAAI,WAAW,CAAC,qBAAqB,EACpC,eACI,8BACA,6BACJ;kEACA,cAAA,8OAAC;4DAAE,WAAU;sEAAW,QAAQ,OAAO;;;;;;;;;;;kEAGzC,8OAAC;wDAAE,WAAW,CAAC,2BAA2B,EAAE,eAAe,eAAe,aAAa;kEACpF,WAAW,QAAQ,UAAU;;;;;;;;;;;;2CApB1B,QAAQ,EAAE;;;;;oCAyBxB;oCAEC,0BACC,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAI,WAAU;wDAAkD,OAAO;4DAAE,gBAAgB;wDAAO;;;;;;kEACjG,8OAAC;wDAAI,WAAU;wDAAkD,OAAO;4DAAE,gBAAgB;wDAAO;;;;;;;;;;;;;;;;;;;;;;kDAMzG,8OAAC;wCAAI,KAAK;;;;;;;;;;;;0CAIZ,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,UAAU;oCAAmB,WAAU;;sDAC3C,8OAAC;4CACC,MAAK;4CACL,OAAO;4CACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4CAC7C,aAAY;4CACZ,WAAU;;;;;;sDAEZ,8OAAC;4CACC,MAAK;4CACL,UAAU,CAAC,WAAW,IAAI;4CAC1B,WAAU;sDAEV,cAAA,8OAAC,sPAAiB;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS/C", "debugId": null}}]}