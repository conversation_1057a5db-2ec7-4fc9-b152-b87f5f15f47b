{"version": 3, "sources": [], "sections": [{"offset": {"line": 22, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent3/agentepub/src/components/auth/ProtectedRoute.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { useAuth } from '@/contexts/AuthContext'\n\ninterface ProtectedRouteProps {\n  children: React.ReactNode\n  redirectTo?: string\n}\n\nexport default function ProtectedRoute({ children, redirectTo = '/' }: ProtectedRouteProps) {\n  const { user, loading } = useAuth()\n  const router = useRouter()\n\n  useEffect(() => {\n    if (!loading && !user) {\n      router.push(redirectTo)\n    }\n  }, [user, loading, router, redirectTo])\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600\"></div>\n      </div>\n    )\n  }\n\n  if (!user) {\n    return null\n  }\n\n  return <>{children}</>\n}\n\n// Loading component for better UX\nexport function LoadingSpinner({ size = 'md' }: { size?: 'sm' | 'md' | 'lg' }) {\n  const sizeClasses = {\n    sm: 'h-4 w-4',\n    md: 'h-8 w-8',\n    lg: 'h-12 w-12'\n  }\n\n  return (\n    <div className=\"flex items-center justify-center\">\n      <div className={`animate-spin rounded-full border-b-2 border-primary-600 ${sizeClasses[size]}`}></div>\n    </div>\n  )\n}\n\n// Auth guard hook for pages\nexport function useAuthGuard(redirectTo = '/') {\n  const { user, loading } = useAuth()\n  const router = useRouter()\n\n  useEffect(() => {\n    if (!loading && !user) {\n      router.push(redirectTo)\n    }\n  }, [user, loading, router, redirectTo])\n\n  return { user, loading, isAuthenticated: !!user }\n}\n"], "names": [], "mappings": ";;;;;;;;;AAEA;AACA;AACA;AAJA;;;;;AAWe,SAAS,eAAe,EAAE,QAAQ,EAAE,aAAa,GAAG,EAAuB;IACxF,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,IAAA,0IAAO;IACjC,MAAM,SAAS,IAAA,+IAAS;IAExB,IAAA,kNAAS,EAAC;QACR,IAAI,CAAC,WAAW,CAAC,MAAM;YACrB,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;QAAM;QAAS;QAAQ;KAAW;IAEtC,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,qBAAO;kBAAG;;AACZ;AAGO,SAAS,eAAe,EAAE,OAAO,IAAI,EAAiC;IAC3E,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAW,CAAC,wDAAwD,EAAE,WAAW,CAAC,KAAK,EAAE;;;;;;;;;;;AAGpG;AAGO,SAAS,aAAa,aAAa,GAAG;IAC3C,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,IAAA,0IAAO;IACjC,MAAM,SAAS,IAAA,+IAAS;IAExB,IAAA,kNAAS,EAAC;QACR,IAAI,CAAC,WAAW,CAAC,MAAM;YACrB,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;QAAM;QAAS;QAAQ;KAAW;IAEtC,OAAO;QAAE;QAAM;QAAS,iBAAiB,CAAC,CAAC;IAAK;AAClD", "debugId": null}}, {"offset": {"line": 119, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent3/agentepub/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatDate(date: string | Date) {\n  return new Intl.DateTimeFormat('pt-BR', {\n    day: '2-digit',\n    month: '2-digit',\n    year: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit',\n  }).format(new Date(date))\n}\n\nexport function formatCurrency(amount: number) {\n  return new Intl.NumberFormat('pt-BR', {\n    style: 'currency',\n    currency: 'BRL',\n  }).format(amount)\n}\n\nexport function getAreaDisplayName(area: string) {\n  const areaNames = {\n    atendimento: 'Atendimento',\n    planejamento: 'Planejamento',\n    midia: 'Mídia'\n  }\n  return areaNames[area as keyof typeof areaNames] || area\n}\n\nexport function generateSlug(text: string) {\n  return text\n    .toLowerCase()\n    .normalize('NFD')\n    .replace(/[\\u0300-\\u036f]/g, '')\n    .replace(/[^a-z0-9\\s-]/g, '')\n    .replace(/\\s+/g, '-')\n    .replace(/-+/g, '-')\n    .trim()\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,IAAA,sKAAO,EAAC,IAAA,6IAAI,EAAC;AACtB;AAEO,SAAS,WAAW,IAAmB;IAC5C,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,KAAK;QACL,OAAO;QACP,MAAM;QACN,MAAM;QACN,QAAQ;IACV,GAAG,MAAM,CAAC,IAAI,KAAK;AACrB;AAEO,SAAS,eAAe,MAAc;IAC3C,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;IACZ,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,mBAAmB,IAAY;IAC7C,MAAM,YAAY;QAChB,aAAa;QACb,cAAc;QACd,OAAO;IACT;IACA,OAAO,SAAS,CAAC,KAA+B,IAAI;AACtD;AAEO,SAAS,aAAa,IAAY;IACvC,OAAO,KACJ,WAAW,GACX,SAAS,CAAC,OACV,OAAO,CAAC,oBAAoB,IAC5B,OAAO,CAAC,iBAAiB,IACzB,OAAO,CAAC,QAAQ,KAChB,OAAO,CAAC,OAAO,KACf,IAAI;AACT", "debugId": null}}, {"offset": {"line": 168, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent3/agentepub/src/components/dashboard/DashboardLayout.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { cn } from '@/lib/utils'\nimport {\n  Bars3Icon,\n  XMarkIcon,\n  HomeIcon,\n  FolderIcon,\n  UserGroupIcon,\n  DocumentChartBarIcon,\n  ChatBubbleLeftRightIcon,\n  Cog6ToothIcon,\n  ArrowRightOnRectangleIcon\n} from '@heroicons/react/24/outline'\n\nconst navigation = [\n  { name: 'Dashboard', href: '/dashboard', icon: HomeIcon },\n  { name: 'Campanhas', href: '/dashboard/campanhas', icon: FolderIcon },\n  { name: 'Agent<PERSON>', href: '/dashboard/agentes', icon: UserGroupIcon },\n  { name: '<PERSON><PERSON><PERSON><PERSON><PERSON>', href: '/dashboard/relatorios', icon: DocumentChartBarIcon },\n  { name: 'Chat', href: '/dashboard/chat', icon: ChatBubbleLeftRightIcon },\n  { name: 'Configura<PERSON>õ<PERSON>', href: '/dashboard/configuracoes', icon: Cog6ToothIcon },\n]\n\ninterface DashboardLayoutProps {\n  children: React.ReactNode\n}\n\nexport default function DashboardLayout({ children }: DashboardLayoutProps) {\n  const [sidebarOpen, setSidebarOpen] = useState(false)\n  const pathname = usePathname()\n  const { user, profile, signOut } = useAuth()\n\n  const handleSignOut = async () => {\n    await signOut()\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Mobile sidebar */}\n      {sidebarOpen && (\n        <div className=\"fixed inset-0 z-50 lg:hidden\">\n          <div className=\"fixed inset-0 bg-gray-600 bg-opacity-75\" onClick={() => setSidebarOpen(false)} />\n          <div className=\"fixed inset-y-0 left-0 flex w-full max-w-xs flex-col bg-white shadow-xl\">\n            <div className=\"flex h-16 items-center justify-between px-6 border-b border-gray-200\">\n              <Link href=\"/dashboard\" className=\"text-xl font-bold text-primary-600\">\n                AgentePub\n              </Link>\n              <button\n                onClick={() => setSidebarOpen(false)}\n                className=\"text-gray-400 hover:text-gray-600\"\n              >\n                <XMarkIcon className=\"h-6 w-6\" />\n              </button>\n            </div>\n            \n            <nav className=\"flex-1 px-6 py-6\">\n              <ul className=\"space-y-2\">\n                {navigation.map((item) => (\n                  <li key={item.name}>\n                    <Link\n                      href={item.href}\n                      className={cn(\n                        'flex items-center gap-3 px-3 py-2 rounded-md text-sm font-medium transition-colors',\n                        pathname === item.href\n                          ? 'bg-primary-100 text-primary-700'\n                          : 'text-gray-700 hover:bg-gray-100'\n                      )}\n                      onClick={() => setSidebarOpen(false)}\n                    >\n                      <item.icon className=\"h-5 w-5\" />\n                      {item.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </nav>\n\n            <div className=\"border-t border-gray-200 p-6\">\n              <div className=\"flex items-center gap-3 mb-4\">\n                <div className=\"h-8 w-8 bg-primary-100 rounded-full flex items-center justify-center\">\n                  <span className=\"text-sm font-medium text-primary-700\">\n                    {profile?.full_name?.charAt(0) || user?.email?.charAt(0) || 'U'}\n                  </span>\n                </div>\n                <div className=\"flex-1 min-w-0\">\n                  <p className=\"text-sm font-medium text-gray-900 truncate\">\n                    {profile?.full_name || user?.email}\n                  </p>\n                  {profile?.area && (\n                    <p className=\"text-xs text-gray-500 capitalize\">{profile.area}</p>\n                  )}\n                </div>\n              </div>\n              <button\n                onClick={handleSignOut}\n                className=\"flex items-center gap-2 text-sm text-gray-700 hover:text-gray-900 transition-colors\"\n              >\n                <ArrowRightOnRectangleIcon className=\"h-4 w-4\" />\n                Sair\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Desktop sidebar */}\n      <div className=\"hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col\">\n        <div className=\"flex flex-col flex-1 bg-white border-r border-gray-200\">\n          <div className=\"flex h-16 items-center px-6 border-b border-gray-200\">\n            <Link href=\"/dashboard\" className=\"text-xl font-bold text-primary-600\">\n              AgentePub\n            </Link>\n          </div>\n          \n          <nav className=\"flex-1 px-6 py-6\">\n            <ul className=\"space-y-2\">\n              {navigation.map((item) => (\n                <li key={item.name}>\n                  <Link\n                    href={item.href}\n                    className={cn(\n                      'flex items-center gap-3 px-3 py-2 rounded-md text-sm font-medium transition-colors',\n                      pathname === item.href\n                        ? 'bg-primary-100 text-primary-700'\n                        : 'text-gray-700 hover:bg-gray-100'\n                    )}\n                  >\n                    <item.icon className=\"h-5 w-5\" />\n                    {item.name}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </nav>\n\n          <div className=\"border-t border-gray-200 p-6\">\n            <div className=\"flex items-center gap-3 mb-4\">\n              <div className=\"h-8 w-8 bg-primary-100 rounded-full flex items-center justify-center\">\n                <span className=\"text-sm font-medium text-primary-700\">\n                  {profile?.full_name?.charAt(0) || user?.email?.charAt(0) || 'U'}\n                </span>\n              </div>\n              <div className=\"flex-1 min-w-0\">\n                <p className=\"text-sm font-medium text-gray-900 truncate\">\n                  {profile?.full_name || user?.email}\n                </p>\n                {profile?.area && (\n                  <p className=\"text-xs text-gray-500 capitalize\">{profile.area}</p>\n                )}\n              </div>\n            </div>\n            <button\n              onClick={handleSignOut}\n              className=\"flex items-center gap-2 text-sm text-gray-700 hover:text-gray-900 transition-colors\"\n            >\n              <ArrowRightOnRectangleIcon className=\"h-4 w-4\" />\n              Sair\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Main content */}\n      <div className=\"lg:pl-64\">\n        {/* Top bar */}\n        <div className=\"sticky top-0 z-40 bg-white border-b border-gray-200 px-6 py-4\">\n          <div className=\"flex items-center justify-between\">\n            <button\n              onClick={() => setSidebarOpen(true)}\n              className=\"lg:hidden text-gray-500 hover:text-gray-700\"\n            >\n              <Bars3Icon className=\"h-6 w-6\" />\n            </button>\n            \n            <div className=\"flex items-center gap-4\">\n              <div className=\"text-sm text-gray-500\">\n                {new Date().toLocaleDateString('pt-BR', { \n                  weekday: 'long', \n                  year: 'numeric', \n                  month: 'long', \n                  day: 'numeric' \n                })}\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Page content */}\n        <main className=\"px-6 py-8\">\n          {children}\n        </main>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAPA;;;;;;;;AAmBA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAa,MAAM;QAAc,MAAM,2NAAQ;IAAC;IACxD;QAAE,MAAM;QAAa,MAAM;QAAwB,MAAM,iOAAU;IAAC;IACpE;QAAE,MAAM;QAAW,MAAM;QAAsB,MAAM,0OAAa;IAAC;IACnE;QAAE,MAAM;QAAc,MAAM;QAAyB,MAAM,+PAAoB;IAAC;IAChF;QAAE,MAAM;QAAQ,MAAM;QAAmB,MAAM,wQAAuB;IAAC;IACvE;QAAE,MAAM;QAAiB,MAAM;QAA4B,MAAM,0OAAa;IAAC;CAChF;AAMc,SAAS,gBAAgB,EAAE,QAAQ,EAAwB;IACxE,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,iNAAQ,EAAC;IAC/C,MAAM,WAAW,IAAA,iJAAW;IAC5B,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,IAAA,0IAAO;IAE1C,MAAM,gBAAgB;QACpB,MAAM;IACR;IAEA,qBACE,8OAAC;QAAI,WAAU;;YAEZ,6BACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;wBAA0C,SAAS,IAAM,eAAe;;;;;;kCACvF,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,uKAAI;wCAAC,MAAK;wCAAa,WAAU;kDAAqC;;;;;;kDAGvE,8OAAC;wCACC,SAAS,IAAM,eAAe;wCAC9B,WAAU;kDAEV,cAAA,8OAAC,8NAAS;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAIzB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAG,WAAU;8CACX,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC;sDACC,cAAA,8OAAC,uKAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAW,IAAA,yHAAE,EACX,sFACA,aAAa,KAAK,IAAI,GAClB,oCACA;gDAEN,SAAS,IAAM,eAAe;;kEAE9B,8OAAC,KAAK,IAAI;wDAAC,WAAU;;;;;;oDACpB,KAAK,IAAI;;;;;;;2CAZL,KAAK,IAAI;;;;;;;;;;;;;;;0CAmBxB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;8DACb,SAAS,WAAW,OAAO,MAAM,MAAM,OAAO,OAAO,MAAM;;;;;;;;;;;0DAGhE,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEACV,SAAS,aAAa,MAAM;;;;;;oDAE9B,SAAS,sBACR,8OAAC;wDAAE,WAAU;kEAAoC,QAAQ,IAAI;;;;;;;;;;;;;;;;;;kDAInE,8OAAC;wCACC,SAAS;wCACT,WAAU;;0DAEV,8OAAC,8QAAyB;gDAAC,WAAU;;;;;;4CAAY;;;;;;;;;;;;;;;;;;;;;;;;;0BAS3D,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,uKAAI;gCAAC,MAAK;gCAAa,WAAU;0CAAqC;;;;;;;;;;;sCAKzE,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAG,WAAU;0CACX,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC;kDACC,cAAA,8OAAC,uKAAI;4CACH,MAAM,KAAK,IAAI;4CACf,WAAW,IAAA,yHAAE,EACX,sFACA,aAAa,KAAK,IAAI,GAClB,oCACA;;8DAGN,8OAAC,KAAK,IAAI;oDAAC,WAAU;;;;;;gDACpB,KAAK,IAAI;;;;;;;uCAXL,KAAK,IAAI;;;;;;;;;;;;;;;sCAkBxB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DACb,SAAS,WAAW,OAAO,MAAM,MAAM,OAAO,OAAO,MAAM;;;;;;;;;;;sDAGhE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DACV,SAAS,aAAa,MAAM;;;;;;gDAE9B,SAAS,sBACR,8OAAC;oDAAE,WAAU;8DAAoC,QAAQ,IAAI;;;;;;;;;;;;;;;;;;8CAInE,8OAAC;oCACC,SAAS;oCACT,WAAU;;sDAEV,8OAAC,8QAAyB;4CAAC,WAAU;;;;;;wCAAY;;;;;;;;;;;;;;;;;;;;;;;;0BAQzD,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAS,IAAM,eAAe;oCAC9B,WAAU;8CAEV,cAAA,8OAAC,8NAAS;wCAAC,WAAU;;;;;;;;;;;8CAGvB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACZ,IAAI,OAAO,kBAAkB,CAAC,SAAS;4CACtC,SAAS;4CACT,MAAM;4CACN,OAAO;4CACP,KAAK;wCACP;;;;;;;;;;;;;;;;;;;;;;kCAOR,8OAAC;wBAAK,WAAU;kCACb;;;;;;;;;;;;;;;;;;AAKX", "debugId": null}}, {"offset": {"line": 636, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent3/agentepub/src/app/dashboard/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\nimport { useSearchParams } from 'next/navigation'\nimport ProtectedRoute from '@/components/auth/ProtectedRoute'\nimport DashboardLayout from '@/components/dashboard/DashboardLayout'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { \n  PlusIcon, \n  ChartBarIcon, \n  ClockIcon, \n  ExclamationTriangleIcon,\n  DocumentTextIcon,\n  UserGroupIcon,\n  CalendarDaysIcon\n} from '@heroicons/react/24/outline'\n\nexport default function DashboardPage() {\n  const { user, profile } = useAuth()\n  const searchParams = useSearchParams()\n  const [showNewCampaignModal, setShowNewCampaignModal] = useState(false)\n\n  useEffect(() => {\n    if (searchParams.get('newCampaign') === 'true') {\n      setShowNewCampaignModal(true)\n    }\n  }, [searchParams])\n\n  const stats = [\n    {\n      name: '<PERSON><PERSON><PERSON>',\n      value: '3',\n      icon: ChartBarIcon,\n      color: 'text-blue-600',\n      bg: 'bg-blue-100'\n    },\n    {\n      name: 'Materiais Gerados',\n      value: '12',\n      icon: DocumentTextIcon,\n      color: 'text-green-600',\n      bg: 'bg-green-100'\n    },\n    {\n      name: 'Prazos Próximos',\n      value: '2',\n      icon: ClockIcon,\n      color: 'text-yellow-600',\n      bg: 'bg-yellow-100'\n    },\n    {\n      name: 'Alertas',\n      value: '1',\n      icon: ExclamationTriangleIcon,\n      color: 'text-red-600',\n      bg: 'bg-red-100'\n    }\n  ]\n\n  const recentActivities = [\n    {\n      id: 1,\n      type: 'briefing',\n      title: 'Briefing processado: Campanha Black Friday',\n      time: '2 horas atrás',\n      agent: 'Atendimento'\n    },\n    {\n      id: 2,\n      type: 'cronograma',\n      title: 'Cronograma gerado: Lançamento App',\n      time: '4 horas atrás',\n      agent: 'Planejamento'\n    },\n    {\n      id: 3,\n      type: 'relatorio',\n      title: 'Relatório de performance: Campanha Verão',\n      time: '1 dia atrás',\n      agent: 'Mídia'\n    }\n  ]\n\n  const quickActions = [\n    {\n      name: 'Criar Campanha',\n      description: 'Inicie uma nova campanha',\n      icon: PlusIcon,\n      action: () => setShowNewCampaignModal(true)\n    },\n    {\n      name: 'Processar Briefing',\n      description: 'Use o agente de atendimento',\n      icon: UserGroupIcon,\n      action: () => console.log('Processar briefing')\n    },\n    {\n      name: 'Gerar Cronograma',\n      description: 'Use o agente de planejamento',\n      icon: CalendarDaysIcon,\n      action: () => console.log('Gerar cronograma')\n    },\n    {\n      name: 'Analisar Performance',\n      description: 'Use o agente de mídia',\n      icon: ChartBarIcon,\n      action: () => console.log('Analisar performance')\n    }\n  ]\n\n  return (\n    <ProtectedRoute>\n      <DashboardLayout>\n        <div className=\"space-y-8\">\n          {/* Welcome Section */}\n          <div>\n            <h1 className=\"text-2xl font-bold text-gray-900\">\n              Bem-vindo, {profile?.full_name || user?.email}!\n            </h1>\n            <p className=\"text-gray-600 mt-1\">\n              {profile?.area ? `Área: ${profile.area.charAt(0).toUpperCase() + profile.area.slice(1)}` : 'Configure seu perfil para começar'}\n            </p>\n          </div>\n\n          {/* Stats Grid */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n            {stats.map((stat) => (\n              <div key={stat.name} className=\"bg-white rounded-lg shadow p-6\">\n                <div className=\"flex items-center\">\n                  <div className={`flex-shrink-0 ${stat.bg} rounded-md p-3`}>\n                    <stat.icon className={`h-6 w-6 ${stat.color}`} />\n                  </div>\n                  <div className=\"ml-4\">\n                    <p className=\"text-sm font-medium text-gray-500\">{stat.name}</p>\n                    <p className=\"text-2xl font-semibold text-gray-900\">{stat.value}</p>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n\n          {/* Quick Actions */}\n          <div>\n            <h2 className=\"text-lg font-semibold text-gray-900 mb-4\">Ações Rápidas</h2>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n              {quickActions.map((action) => (\n                <button\n                  key={action.name}\n                  onClick={action.action}\n                  className=\"bg-white rounded-lg shadow p-6 text-left hover:shadow-md transition-shadow\"\n                >\n                  <div className=\"flex items-center mb-3\">\n                    <action.icon className=\"h-6 w-6 text-primary-600\" />\n                    <h3 className=\"ml-2 font-medium text-gray-900\">{action.name}</h3>\n                  </div>\n                  <p className=\"text-sm text-gray-600\">{action.description}</p>\n                </button>\n              ))}\n            </div>\n          </div>\n\n          {/* Recent Activities */}\n          <div>\n            <h2 className=\"text-lg font-semibold text-gray-900 mb-4\">Atividades Recentes</h2>\n            <div className=\"bg-white rounded-lg shadow\">\n              <div className=\"divide-y divide-gray-200\">\n                {recentActivities.map((activity) => (\n                  <div key={activity.id} className=\"p-6\">\n                    <div className=\"flex items-center justify-between\">\n                      <div>\n                        <p className=\"text-sm font-medium text-gray-900\">{activity.title}</p>\n                        <p className=\"text-sm text-gray-500\">Agente: {activity.agent}</p>\n                      </div>\n                      <p className=\"text-sm text-gray-500\">{activity.time}</p>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n          </div>\n\n          {/* Getting Started */}\n          {!profile?.area && (\n            <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-6\">\n              <h3 className=\"text-lg font-semibold text-blue-900 mb-2\">Complete seu perfil</h3>\n              <p className=\"text-blue-700 mb-4\">\n                Defina sua área de atuação para receber recomendações personalizadas dos agentes.\n              </p>\n              <button className=\"bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700 transition-colors\">\n                Configurar Perfil\n              </button>\n            </div>\n          )}\n        </div>\n\n        {/* New Campaign Modal */}\n        {showNewCampaignModal && (\n          <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\">\n            <div className=\"bg-white rounded-lg max-w-md w-full p-6\">\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Nova Campanha</h3>\n              <p className=\"text-gray-600 mb-4\">\n                Funcionalidade em desenvolvimento. Em breve você poderá criar campanhas diretamente aqui!\n              </p>\n              <div className=\"flex gap-3\">\n                <button\n                  onClick={() => setShowNewCampaignModal(false)}\n                  className=\"flex-1 bg-gray-200 text-gray-800 py-2 px-4 rounded-md font-medium hover:bg-gray-300 transition-colors\"\n                >\n                  Fechar\n                </button>\n                <button\n                  onClick={() => {\n                    setShowNewCampaignModal(false)\n                    window.location.href = '/teste'\n                  }}\n                  className=\"flex-1 bg-primary-600 text-white py-2 px-4 rounded-md font-medium hover:bg-primary-700 transition-colors\"\n                >\n                  Testar Agentes\n                </button>\n              </div>\n            </div>\n          </div>\n        )}\n      </DashboardLayout>\n    </ProtectedRoute>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAPA;;;;;;;;AAiBe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,IAAA,0IAAO;IACjC,MAAM,eAAe,IAAA,qJAAe;IACpC,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,IAAA,iNAAQ,EAAC;IAEjE,IAAA,kNAAS,EAAC;QACR,IAAI,aAAa,GAAG,CAAC,mBAAmB,QAAQ;YAC9C,wBAAwB;QAC1B;IACF,GAAG;QAAC;KAAa;IAEjB,MAAM,QAAQ;QACZ;YACE,MAAM;YACN,OAAO;YACP,MAAM,uOAAY;YAClB,OAAO;YACP,IAAI;QACN;QACA;YACE,MAAM;YACN,OAAO;YACP,MAAM,mPAAgB;YACtB,OAAO;YACP,IAAI;QACN;QACA;YACE,MAAM;YACN,OAAO;YACP,MAAM,8NAAS;YACf,OAAO;YACP,IAAI;QACN;QACA;YACE,MAAM;YACN,OAAO;YACP,MAAM,wQAAuB;YAC7B,OAAO;YACP,IAAI;QACN;KACD;IAED,MAAM,mBAAmB;QACvB;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,MAAM;YACN,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,MAAM;YACN,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,MAAM;YACN,OAAO;QACT;KACD;IAED,MAAM,eAAe;QACnB;YACE,MAAM;YACN,aAAa;YACb,MAAM,2NAAQ;YACd,QAAQ,IAAM,wBAAwB;QACxC;QACA;YACE,MAAM;YACN,aAAa;YACb,MAAM,0OAAa;YACnB,QAAQ,IAAM,QAAQ,GAAG,CAAC;QAC5B;QACA;YACE,MAAM;YACN,aAAa;YACb,MAAM,mPAAgB;YACtB,QAAQ,IAAM,QAAQ,GAAG,CAAC;QAC5B;QACA;YACE,MAAM;YACN,aAAa;YACb,MAAM,uOAAY;YAClB,QAAQ,IAAM,QAAQ,GAAG,CAAC;QAC5B;KACD;IAED,qBACE,8OAAC,uJAAc;kBACb,cAAA,8OAAC,6JAAe;;8BACd,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;;wCAAmC;wCACnC,SAAS,aAAa,MAAM;wCAAM;;;;;;;8CAEhD,8OAAC;oCAAE,WAAU;8CACV,SAAS,OAAO,CAAC,MAAM,EAAE,QAAQ,IAAI,CAAC,MAAM,CAAC,GAAG,WAAW,KAAK,QAAQ,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG;;;;;;;;;;;;sCAK/F,8OAAC;4BAAI,WAAU;sCACZ,MAAM,GAAG,CAAC,CAAC,qBACV,8OAAC;oCAAoB,WAAU;8CAC7B,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAW,CAAC,cAAc,EAAE,KAAK,EAAE,CAAC,eAAe,CAAC;0DACvD,cAAA,8OAAC,KAAK,IAAI;oDAAC,WAAW,CAAC,QAAQ,EAAE,KAAK,KAAK,EAAE;;;;;;;;;;;0DAE/C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEAAqC,KAAK,IAAI;;;;;;kEAC3D,8OAAC;wDAAE,WAAU;kEAAwC,KAAK,KAAK;;;;;;;;;;;;;;;;;;mCAP3D,KAAK,IAAI;;;;;;;;;;sCAevB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA2C;;;;;;8CACzD,8OAAC;oCAAI,WAAU;8CACZ,aAAa,GAAG,CAAC,CAAC,uBACjB,8OAAC;4CAEC,SAAS,OAAO,MAAM;4CACtB,WAAU;;8DAEV,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,OAAO,IAAI;4DAAC,WAAU;;;;;;sEACvB,8OAAC;4DAAG,WAAU;sEAAkC,OAAO,IAAI;;;;;;;;;;;;8DAE7D,8OAAC;oDAAE,WAAU;8DAAyB,OAAO,WAAW;;;;;;;2CARnD,OAAO,IAAI;;;;;;;;;;;;;;;;sCAexB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA2C;;;;;;8CACzD,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACZ,iBAAiB,GAAG,CAAC,CAAC,yBACrB,8OAAC;gDAAsB,WAAU;0DAC/B,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;8EACC,8OAAC;oEAAE,WAAU;8EAAqC,SAAS,KAAK;;;;;;8EAChE,8OAAC;oEAAE,WAAU;;wEAAwB;wEAAS,SAAS,KAAK;;;;;;;;;;;;;sEAE9D,8OAAC;4DAAE,WAAU;sEAAyB,SAAS,IAAI;;;;;;;;;;;;+CAN7C,SAAS,EAAE;;;;;;;;;;;;;;;;;;;;;wBAe5B,CAAC,SAAS,sBACT,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA2C;;;;;;8CACzD,8OAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAGlC,8OAAC;oCAAO,WAAU;8CAAsG;;;;;;;;;;;;;;;;;;gBAQ7H,sCACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA2C;;;;;;0CACzD,8OAAC;gCAAE,WAAU;0CAAqB;;;;;;0CAGlC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,SAAS,IAAM,wBAAwB;wCACvC,WAAU;kDACX;;;;;;kDAGD,8OAAC;wCACC,SAAS;4CACP,wBAAwB;4CACxB,OAAO,QAAQ,CAAC,IAAI,GAAG;wCACzB;wCACA,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB", "debugId": null}}]}