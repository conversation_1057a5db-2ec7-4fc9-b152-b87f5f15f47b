{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent3/agentepub/src/components/auth/AuthModal.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Dialog, DialogPanel, DialogTitle } from '@headlessui/react'\nimport { XMarkIcon } from '@heroicons/react/24/outline'\nimport { useAuth } from '@/contexts/AuthContext'\n\ninterface AuthModalProps {\n  isOpen: boolean\n  onClose: () => void\n  defaultMode?: 'login' | 'signup'\n}\n\nexport default function AuthModal({ isOpen, onClose, defaultMode = 'login' }: AuthModalProps) {\n  const [mode, setMode] = useState<'login' | 'signup'>(defaultMode)\n  const [email, setEmail] = useState('')\n  const [password, setPassword] = useState('')\n  const [fullName, setFullName] = useState('')\n  const [loading, setLoading] = useState(false)\n  const [error, setError] = useState('')\n\n  const { signIn, signUp } = useAuth()\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    setLoading(true)\n    setError('')\n\n    try {\n      let result\n      if (mode === 'login') {\n        result = await signIn(email, password)\n      } else {\n        result = await signUp(email, password, fullName)\n      }\n\n      if (result.error) {\n        setError(result.error.message || 'Ocorreu um erro')\n      } else {\n        onClose()\n        // Reset form\n        setEmail('')\n        setPassword('')\n        setFullName('')\n      }\n    } catch (err) {\n      setError('Ocorreu um erro inesperado')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const resetForm = () => {\n    setEmail('')\n    setPassword('')\n    setFullName('')\n    setError('')\n  }\n\n  const switchMode = () => {\n    setMode(mode === 'login' ? 'signup' : 'login')\n    resetForm()\n  }\n\n  return (\n    <Dialog open={isOpen} onClose={onClose} className=\"relative z-50\">\n      <div className=\"fixed inset-0 bg-black/30\" aria-hidden=\"true\" />\n\n      <div className=\"fixed inset-0 flex items-center justify-center p-4\">\n        <DialogPanel className=\"mx-auto max-w-md w-full bg-white rounded-lg shadow-xl\">\n          <div className=\"flex items-center justify-between p-6 border-b\">\n            <DialogTitle className=\"text-lg font-semibold text-gray-900\">\n              {mode === 'login' ? 'Entrar' : 'Criar Conta'}\n            </DialogTitle>\n            <button\n              onClick={onClose}\n              className=\"text-gray-400 hover:text-gray-600 transition-colors\"\n            >\n              <XMarkIcon className=\"h-6 w-6\" />\n            </button>\n          </div>\n\n          <form onSubmit={handleSubmit} className=\"p-6 space-y-4\">\n            {error && (\n              <div className=\"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md text-sm\">\n                {error}\n              </div>\n            )}\n\n            {mode === 'signup' && (\n              <div>\n                <label htmlFor=\"fullName\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Nome Completo\n                </label>\n                <input\n                  id=\"fullName\"\n                  type=\"text\"\n                  value={fullName}\n                  onChange={(e) => setFullName(e.target.value)}\n                  required\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n                  placeholder=\"Seu nome completo\"\n                />\n              </div>\n            )}\n\n            <div>\n              <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                E-mail\n              </label>\n              <input\n                id=\"email\"\n                type=\"email\"\n                value={email}\n                onChange={(e) => setEmail(e.target.value)}\n                required\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n                placeholder=\"<EMAIL>\"\n              />\n            </div>\n\n            <div>\n              <label htmlFor=\"password\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                Senha\n              </label>\n              <input\n                id=\"password\"\n                type=\"password\"\n                value={password}\n                onChange={(e) => setPassword(e.target.value)}\n                required\n                minLength={6}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n                placeholder=\"Mínimo 6 caracteres\"\n              />\n            </div>\n\n            <button\n              type=\"submit\"\n              disabled={loading}\n              className={`w-full bg-blue-600 text-white py-3 px-4 rounded-md font-medium transition-colors ${loading\n                ? \"opacity-50 cursor-not-allowed\"\n                : \"hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\"\n                }`}\n            >\n              {loading ? 'Carregando...' : mode === 'login' ? 'Entrar' : 'Criar Conta'}\n            </button>\n\n            <div className=\"text-center\">\n              <button\n                type=\"button\"\n                onClick={switchMode}\n                className=\"text-sm text-blue-600 hover:text-blue-700 transition-colors\"\n              >\n                {mode === 'login'\n                  ? 'Não tem conta? Criar uma agora'\n                  : 'Já tem conta? Fazer login'\n                }\n              </button>\n            </div>\n          </form>\n        </DialogPanel>\n      </div>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAae,SAAS,UAAU,KAA0D;QAA1D,EAAE,MAAM,EAAE,OAAO,EAAE,cAAc,OAAO,EAAkB,GAA1D;;IAChC,MAAM,CAAC,MAAM,QAAQ,GAAG,IAAA,yKAAQ,EAAqB;IACrD,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,yKAAQ,EAAC;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,yKAAQ,EAAC;IACzC,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,yKAAQ,EAAC;IACzC,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,yKAAQ,EAAC;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,yKAAQ,EAAC;IAEnC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,IAAA,6IAAO;IAElC,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,WAAW;QACX,SAAS;QAET,IAAI;YACF,IAAI;YACJ,IAAI,SAAS,SAAS;gBACpB,SAAS,MAAM,OAAO,OAAO;YAC/B,OAAO;gBACL,SAAS,MAAM,OAAO,OAAO,UAAU;YACzC;YAEA,IAAI,OAAO,KAAK,EAAE;gBAChB,SAAS,OAAO,KAAK,CAAC,OAAO,IAAI;YACnC,OAAO;gBACL;gBACA,aAAa;gBACb,SAAS;gBACT,YAAY;gBACZ,YAAY;YACd;QACF,EAAE,OAAO,KAAK;YACZ,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,YAAY;QAChB,SAAS;QACT,YAAY;QACZ,YAAY;QACZ,SAAS;IACX;IAEA,MAAM,aAAa;QACjB,QAAQ,SAAS,UAAU,WAAW;QACtC;IACF;IAEA,qBACE,6LAAC,4LAAM;QAAC,MAAM;QAAQ,SAAS;QAAS,WAAU;;0BAChD,6LAAC;gBAAI,WAAU;gBAA4B,eAAY;;;;;;0BAEvD,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,iMAAW;oBAAC,WAAU;;sCACrB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,iMAAW;oCAAC,WAAU;8CACpB,SAAS,UAAU,WAAW;;;;;;8CAEjC,6LAAC;oCACC,SAAS;oCACT,WAAU;8CAEV,cAAA,6LAAC,iOAAS;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAIzB,6LAAC;4BAAK,UAAU;4BAAc,WAAU;;gCACrC,uBACC,6LAAC;oCAAI,WAAU;8CACZ;;;;;;gCAIJ,SAAS,0BACR,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAW,WAAU;sDAA+C;;;;;;sDAGnF,6LAAC;4CACC,IAAG;4CACH,MAAK;4CACL,OAAO;4CACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;4CAC3C,QAAQ;4CACR,WAAU;4CACV,aAAY;;;;;;;;;;;;8CAKlB,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAQ,WAAU;sDAA+C;;;;;;sDAGhF,6LAAC;4CACC,IAAG;4CACH,MAAK;4CACL,OAAO;4CACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;4CACxC,QAAQ;4CACR,WAAU;4CACV,aAAY;;;;;;;;;;;;8CAIhB,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAW,WAAU;sDAA+C;;;;;;sDAGnF,6LAAC;4CACC,IAAG;4CACH,MAAK;4CACL,OAAO;4CACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;4CAC3C,QAAQ;4CACR,WAAW;4CACX,WAAU;4CACV,aAAY;;;;;;;;;;;;8CAIhB,6LAAC;oCACC,MAAK;oCACL,UAAU;oCACV,WAAW,AAAC,oFAGT,OAH4F,UAC3F,kCACA;8CAGH,UAAU,kBAAkB,SAAS,UAAU,WAAW;;;;;;8CAG7D,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCACC,MAAK;wCACL,SAAS;wCACT,WAAU;kDAET,SAAS,UACN,mCACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASpB;GAxJwB;;QAQK,6IAAO;;;KARZ", "debugId": null}}, {"offset": {"line": 285, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent3/agentepub/src/components/auth/SimpleAuthModal.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useAuth } from '@/contexts/AuthContext'\n\ninterface SimpleAuthModalProps {\n  isOpen: boolean\n  onClose: () => void\n  defaultMode?: 'login' | 'signup'\n}\n\nexport default function SimpleAuthModal({ isOpen, onClose, defaultMode = 'login' }: SimpleAuthModalProps) {\n  const [mode, setMode] = useState<'login' | 'signup'>(defaultMode)\n  const [email, setEmail] = useState('')\n  const [password, setPassword] = useState('')\n  const [fullName, setFullName] = useState('')\n  const [loading, setLoading] = useState(false)\n  const [error, setError] = useState('')\n\n  const { signIn, signUp } = useAuth()\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    setLoading(true)\n    setError('')\n\n    console.log('🚀 Form submitted:', { mode, email, hasPassword: !!password, hasFullName: !!fullName })\n\n    try {\n      let result\n      if (mode === 'login') {\n        console.log('🔐 Calling signIn...')\n        result = await signIn(email, password)\n      } else {\n        console.log('📝 Calling signUp...')\n        result = await signUp(email, password, fullName)\n      }\n\n      console.log('📋 Auth result:', result)\n\n      if (result.error) {\n        console.error('❌ Auth error:', result.error)\n        setError(result.error.message || 'Ocorreu um erro')\n      } else {\n        console.log('✅ Auth successful, closing modal')\n        onClose()\n        // Reset form\n        setEmail('')\n        setPassword('')\n        setFullName('')\n      }\n    } catch (err) {\n      console.error('❌ Exception in handleSubmit:', err)\n      setError('Ocorreu um erro inesperado: ' + (err instanceof Error ? err.message : String(err)))\n    } finally {\n      console.log('🏁 Setting loading to false')\n      setLoading(false)\n    }\n  }\n\n  const resetForm = () => {\n    setEmail('')\n    setPassword('')\n    setFullName('')\n    setError('')\n  }\n\n  const switchMode = () => {\n    setMode(mode === 'login' ? 'signup' : 'login')\n    resetForm()\n  }\n\n  if (!isOpen) return null\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\">\n      <div className=\"bg-white rounded-lg max-w-md w-full p-6 relative\">\n        {/* Close button */}\n        <button\n          onClick={onClose}\n          className=\"absolute top-4 right-4 text-gray-400 hover:text-gray-600 text-2xl\"\n        >\n          ×\n        </button>\n\n        {/* Title */}\n        <h2 className=\"text-xl font-bold text-gray-900 mb-6\">\n          {mode === 'login' ? 'Entrar' : 'Criar Conta'}\n        </h2>\n\n        {/* Form */}\n        <form onSubmit={handleSubmit} className=\"space-y-4\">\n          {error && (\n            <div className=\"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md text-sm\">\n              {error}\n            </div>\n          )}\n\n          {mode === 'signup' && (\n            <div>\n              <label htmlFor=\"fullName\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                Nome Completo\n              </label>\n              <input\n                id=\"fullName\"\n                type=\"text\"\n                value={fullName}\n                onChange={(e) => setFullName(e.target.value)}\n                required\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                placeholder=\"Seu nome completo\"\n              />\n            </div>\n          )}\n\n          <div>\n            <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n              E-mail\n            </label>\n            <input\n              id=\"email\"\n              type=\"email\"\n              value={email}\n              onChange={(e) => setEmail(e.target.value)}\n              required\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              placeholder=\"<EMAIL>\"\n            />\n          </div>\n\n          <div>\n            <label htmlFor=\"password\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n              Senha\n            </label>\n            <input\n              id=\"password\"\n              type=\"password\"\n              value={password}\n              onChange={(e) => setPassword(e.target.value)}\n              required\n              minLength={6}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              placeholder=\"Mínimo 6 caracteres\"\n            />\n          </div>\n\n          {/* SUBMIT BUTTON - DESTACADO */}\n          <button\n            type=\"submit\"\n            disabled={loading}\n            className=\"w-full bg-blue-600 text-white py-3 px-4 rounded-md font-semibold text-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors border-2 border-blue-600\"\n            style={{ minHeight: '48px' }}\n          >\n            {loading ? 'Carregando...' : mode === 'login' ? '🚀 ENTRAR' : '✨ CRIAR CONTA'}\n          </button>\n\n          <div className=\"text-center pt-4\">\n            <button\n              type=\"button\"\n              onClick={switchMode}\n              className=\"text-sm text-blue-600 hover:text-blue-700 transition-colors underline\"\n            >\n              {mode === 'login'\n                ? 'Não tem conta? Criar uma agora'\n                : 'Já tem conta? Fazer login'\n              }\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;;;AAHA;;;AAWe,SAAS,gBAAgB,KAAgE;QAAhE,EAAE,MAAM,EAAE,OAAO,EAAE,cAAc,OAAO,EAAwB,GAAhE;;IACtC,MAAM,CAAC,MAAM,QAAQ,GAAG,IAAA,yKAAQ,EAAqB;IACrD,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,yKAAQ,EAAC;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,yKAAQ,EAAC;IACzC,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,yKAAQ,EAAC;IACzC,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,yKAAQ,EAAC;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,yKAAQ,EAAC;IAEnC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,IAAA,6IAAO;IAElC,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,WAAW;QACX,SAAS;QAET,QAAQ,GAAG,CAAC,sBAAsB;YAAE;YAAM;YAAO,aAAa,CAAC,CAAC;YAAU,aAAa,CAAC,CAAC;QAAS;QAElG,IAAI;YACF,IAAI;YACJ,IAAI,SAAS,SAAS;gBACpB,QAAQ,GAAG,CAAC;gBACZ,SAAS,MAAM,OAAO,OAAO;YAC/B,OAAO;gBACL,QAAQ,GAAG,CAAC;gBACZ,SAAS,MAAM,OAAO,OAAO,UAAU;YACzC;YAEA,QAAQ,GAAG,CAAC,mBAAmB;YAE/B,IAAI,OAAO,KAAK,EAAE;gBAChB,QAAQ,KAAK,CAAC,iBAAiB,OAAO,KAAK;gBAC3C,SAAS,OAAO,KAAK,CAAC,OAAO,IAAI;YACnC,OAAO;gBACL,QAAQ,GAAG,CAAC;gBACZ;gBACA,aAAa;gBACb,SAAS;gBACT,YAAY;gBACZ,YAAY;YACd;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,SAAS,iCAAiC,CAAC,eAAe,QAAQ,IAAI,OAAO,GAAG,OAAO,IAAI;QAC7F,SAAU;YACR,QAAQ,GAAG,CAAC;YACZ,WAAW;QACb;IACF;IAEA,MAAM,YAAY;QAChB,SAAS;QACT,YAAY;QACZ,YAAY;QACZ,SAAS;IACX;IAEA,MAAM,aAAa;QACjB,QAAQ,SAAS,UAAU,WAAW;QACtC;IACF;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBACC,SAAS;oBACT,WAAU;8BACX;;;;;;8BAKD,6LAAC;oBAAG,WAAU;8BACX,SAAS,UAAU,WAAW;;;;;;8BAIjC,6LAAC;oBAAK,UAAU;oBAAc,WAAU;;wBACrC,uBACC,6LAAC;4BAAI,WAAU;sCACZ;;;;;;wBAIJ,SAAS,0BACR,6LAAC;;8CACC,6LAAC;oCAAM,SAAQ;oCAAW,WAAU;8CAA+C;;;;;;8CAGnF,6LAAC;oCACC,IAAG;oCACH,MAAK;oCACL,OAAO;oCACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;oCAC3C,QAAQ;oCACR,WAAU;oCACV,aAAY;;;;;;;;;;;;sCAKlB,6LAAC;;8CACC,6LAAC;oCAAM,SAAQ;oCAAQ,WAAU;8CAA+C;;;;;;8CAGhF,6LAAC;oCACC,IAAG;oCACH,MAAK;oCACL,OAAO;oCACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;oCACxC,QAAQ;oCACR,WAAU;oCACV,aAAY;;;;;;;;;;;;sCAIhB,6LAAC;;8CACC,6LAAC;oCAAM,SAAQ;oCAAW,WAAU;8CAA+C;;;;;;8CAGnF,6LAAC;oCACC,IAAG;oCACH,MAAK;oCACL,OAAO;oCACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;oCAC3C,QAAQ;oCACR,WAAW;oCACX,WAAU;oCACV,aAAY;;;;;;;;;;;;sCAKhB,6LAAC;4BACC,MAAK;4BACL,UAAU;4BACV,WAAU;4BACV,OAAO;gCAAE,WAAW;4BAAO;sCAE1B,UAAU,kBAAkB,SAAS,UAAU,cAAc;;;;;;sCAGhE,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,MAAK;gCACL,SAAS;gCACT,WAAU;0CAET,SAAS,UACN,mCACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQlB;GAjKwB;;QAQK,6IAAO;;;KARZ", "debugId": null}}, {"offset": {"line": 545, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent3/agentepub/src/app/test-modal/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport AuthModal from '@/components/auth/AuthModal'\nimport SimpleAuthModal from '@/components/auth/SimpleAuthModal'\nimport TestAuthModal from '@/components/auth/TestAuthModal'\n\nexport default function TestModalPage() {\n  const [isOpen, setIsOpen] = useState(false)\n  const [isSimpleOpen, setIsSimpleOpen] = useState(false)\n  const [isTestOpen, setIsTestOpen] = useState(false)\n\n  const testSignUp = () => {\n    setIsSimpleOpen(true)\n    // Pre-fill with test data after modal opens\n    setTimeout(() => {\n      const emailInput = document.getElementById('email') as HTMLInputElement\n      const passwordInput = document.getElementById('password') as HTMLInputElement\n      const nameInput = document.getElementById('fullName') as HTMLInputElement\n\n      if (emailInput) {\n        emailInput.value = '<EMAIL>'\n        emailInput.dispatchEvent(new Event('input', { bubbles: true }))\n      }\n      if (passwordInput) {\n        passwordInput.value = '123456'\n        passwordInput.dispatchEvent(new Event('input', { bubbles: true }))\n      }\n      if (nameInput) {\n        nameInput.value = 'Usuário Teste AgentePub'\n        nameInput.dispatchEvent(new Event('input', { bubbles: true }))\n      }\n\n      console.log('✅ Test data filled:', {\n        email: emailInput?.value,\n        password: passwordInput?.value ? '***' : 'empty',\n        name: nameInput?.value\n      })\n    }, 200)\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-100 flex items-center justify-center\">\n      <div className=\"bg-white p-8 rounded-lg shadow-lg max-w-md w-full\">\n        <h1 className=\"text-2xl font-bold mb-6\">Teste dos Modais de Autenticação</h1>\n\n        <div className=\"space-y-4\">\n          <button\n            onClick={() => setIsOpen(true)}\n            className=\"w-full bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700\"\n          >\n            Abrir Modal Original (Headless UI)\n          </button>\n\n          <button\n            onClick={() => setIsSimpleOpen(true)}\n            className=\"w-full bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700\"\n          >\n            Abrir Modal Simples (HTML Puro)\n          </button>\n\n          <button\n            onClick={() => setIsTestOpen(true)}\n            className=\"w-full bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700\"\n          >\n            🧪 Modal de Teste (Pré-preenchido)\n          </button>\n        </div>\n\n        <div className=\"mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-md\">\n          <h3 className=\"font-medium text-yellow-800 mb-2\">Instruções:</h3>\n          <ul className=\"text-sm text-yellow-700 space-y-1\">\n            <li>• Use o botão \"Teste Rápido\" para cadastro automático</li>\n            <li>• Ou crie manualmente: <EMAIL> / 123456</li>\n            <li>• Abra o Console do navegador (F12) para ver logs</li>\n          </ul>\n        </div>\n\n        <AuthModal\n          isOpen={isOpen}\n          onClose={() => setIsOpen(false)}\n        />\n\n        <SimpleAuthModal\n          isOpen={isSimpleOpen}\n          onClose={() => setIsSimpleOpen(false)}\n        />\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;;;AAJA;;;;AAOe,SAAS;;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,IAAA,yKAAQ,EAAC;IACrC,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,yKAAQ,EAAC;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,yKAAQ,EAAC;IAE7C,MAAM,aAAa;QACjB,gBAAgB;QAChB,4CAA4C;QAC5C,WAAW;YACT,MAAM,aAAa,SAAS,cAAc,CAAC;YAC3C,MAAM,gBAAgB,SAAS,cAAc,CAAC;YAC9C,MAAM,YAAY,SAAS,cAAc,CAAC;YAE1C,IAAI,YAAY;gBACd,WAAW,KAAK,GAAG;gBACnB,WAAW,aAAa,CAAC,IAAI,MAAM,SAAS;oBAAE,SAAS;gBAAK;YAC9D;YACA,IAAI,eAAe;gBACjB,cAAc,KAAK,GAAG;gBACtB,cAAc,aAAa,CAAC,IAAI,MAAM,SAAS;oBAAE,SAAS;gBAAK;YACjE;YACA,IAAI,WAAW;gBACb,UAAU,KAAK,GAAG;gBAClB,UAAU,aAAa,CAAC,IAAI,MAAM,SAAS;oBAAE,SAAS;gBAAK;YAC7D;YAEA,QAAQ,GAAG,CAAC,uBAAuB;gBACjC,KAAK,EAAE,uBAAA,iCAAA,WAAY,KAAK;gBACxB,UAAU,CAAA,0BAAA,oCAAA,cAAe,KAAK,IAAG,QAAQ;gBACzC,IAAI,EAAE,sBAAA,gCAAA,UAAW,KAAK;YACxB;QACF,GAAG;IACL;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAG,WAAU;8BAA0B;;;;;;8BAExC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,SAAS,IAAM,UAAU;4BACzB,WAAU;sCACX;;;;;;sCAID,6LAAC;4BACC,SAAS,IAAM,gBAAgB;4BAC/B,WAAU;sCACX;;;;;;sCAID,6LAAC;4BACC,SAAS,IAAM,cAAc;4BAC7B,WAAU;sCACX;;;;;;;;;;;;8BAKH,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAmC;;;;;;sCACjD,6LAAC;4BAAG,WAAU;;8CACZ,6LAAC;8CAAG;;;;;;8CACJ,6LAAC;8CAAG;;;;;;8CACJ,6LAAC;8CAAG;;;;;;;;;;;;;;;;;;8BAIR,6LAAC,qJAAS;oBACR,QAAQ;oBACR,SAAS,IAAM,UAAU;;;;;;8BAG3B,6LAAC,2JAAe;oBACd,QAAQ;oBACR,SAAS,IAAM,gBAAgB;;;;;;;;;;;;;;;;;AAKzC;GAnFwB;KAAA", "debugId": null}}]}