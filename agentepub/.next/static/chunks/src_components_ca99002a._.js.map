{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent3/agentepub/src/components/auth/AuthModal.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Dialog, DialogPanel, DialogTitle } from '@headlessui/react'\nimport { XMarkIcon } from '@heroicons/react/24/outline'\nimport { useAuth } from '@/contexts/AuthContext'\n\ninterface AuthModalProps {\n  isOpen: boolean\n  onClose: () => void\n  defaultMode?: 'login' | 'signup'\n}\n\nexport default function AuthModal({ isOpen, onClose, defaultMode = 'login' }: AuthModalProps) {\n  const [mode, setMode] = useState<'login' | 'signup'>(defaultMode)\n  const [email, setEmail] = useState('')\n  const [password, setPassword] = useState('')\n  const [fullName, setFullName] = useState('')\n  const [loading, setLoading] = useState(false)\n  const [error, setError] = useState('')\n\n  const { signIn, signUp } = useAuth()\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    setLoading(true)\n    setError('')\n\n    try {\n      let result\n      if (mode === 'login') {\n        result = await signIn(email, password)\n      } else {\n        result = await signUp(email, password, fullName)\n      }\n\n      if (result.error) {\n        setError(result.error.message || 'Ocorreu um erro')\n      } else {\n        onClose()\n        // Reset form\n        setEmail('')\n        setPassword('')\n        setFullName('')\n      }\n    } catch (err) {\n      setError('Ocorreu um erro inesperado')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const resetForm = () => {\n    setEmail('')\n    setPassword('')\n    setFullName('')\n    setError('')\n  }\n\n  const switchMode = () => {\n    setMode(mode === 'login' ? 'signup' : 'login')\n    resetForm()\n  }\n\n  return (\n    <Dialog open={isOpen} onClose={onClose} className=\"relative z-50\">\n      <div className=\"fixed inset-0 bg-black/30\" aria-hidden=\"true\" />\n\n      <div className=\"fixed inset-0 flex items-center justify-center p-4\">\n        <DialogPanel className=\"mx-auto max-w-md w-full bg-white rounded-lg shadow-xl\">\n          <div className=\"flex items-center justify-between p-6 border-b\">\n            <DialogTitle className=\"text-lg font-semibold text-gray-900\">\n              {mode === 'login' ? 'Entrar' : 'Criar Conta'}\n            </DialogTitle>\n            <button\n              onClick={onClose}\n              className=\"text-gray-400 hover:text-gray-600 transition-colors\"\n            >\n              <XMarkIcon className=\"h-6 w-6\" />\n            </button>\n          </div>\n\n          <form onSubmit={handleSubmit} className=\"p-6 space-y-4\">\n            {error && (\n              <div className=\"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md text-sm\">\n                {error}\n              </div>\n            )}\n\n            {mode === 'signup' && (\n              <div>\n                <label htmlFor=\"fullName\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Nome Completo\n                </label>\n                <input\n                  id=\"fullName\"\n                  type=\"text\"\n                  value={fullName}\n                  onChange={(e) => setFullName(e.target.value)}\n                  required\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n                  placeholder=\"Seu nome completo\"\n                />\n              </div>\n            )}\n\n            <div>\n              <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                E-mail\n              </label>\n              <input\n                id=\"email\"\n                type=\"email\"\n                value={email}\n                onChange={(e) => setEmail(e.target.value)}\n                required\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n                placeholder=\"<EMAIL>\"\n              />\n            </div>\n\n            <div>\n              <label htmlFor=\"password\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                Senha\n              </label>\n              <input\n                id=\"password\"\n                type=\"password\"\n                value={password}\n                onChange={(e) => setPassword(e.target.value)}\n                required\n                minLength={6}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n                placeholder=\"Mínimo 6 caracteres\"\n              />\n            </div>\n\n            <button\n              type=\"submit\"\n              disabled={loading}\n              className={`w-full bg-blue-600 text-white py-3 px-4 rounded-md font-medium transition-colors ${loading\n                ? \"opacity-50 cursor-not-allowed\"\n                : \"hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\"\n                }`}\n            >\n              {loading ? 'Carregando...' : mode === 'login' ? 'Entrar' : 'Criar Conta'}\n            </button>\n\n            <div className=\"text-center\">\n              <button\n                type=\"button\"\n                onClick={switchMode}\n                className=\"text-sm text-blue-600 hover:text-blue-700 transition-colors\"\n              >\n                {mode === 'login'\n                  ? 'Não tem conta? Criar uma agora'\n                  : 'Já tem conta? Fazer login'\n                }\n              </button>\n            </div>\n          </form>\n        </DialogPanel>\n      </div>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAae,SAAS,UAAU,KAA0D;QAA1D,EAAE,MAAM,EAAE,OAAO,EAAE,cAAc,OAAO,EAAkB,GAA1D;;IAChC,MAAM,CAAC,MAAM,QAAQ,GAAG,IAAA,yKAAQ,EAAqB;IACrD,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,yKAAQ,EAAC;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,yKAAQ,EAAC;IACzC,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,yKAAQ,EAAC;IACzC,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,yKAAQ,EAAC;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,yKAAQ,EAAC;IAEnC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,IAAA,6IAAO;IAElC,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,WAAW;QACX,SAAS;QAET,IAAI;YACF,IAAI;YACJ,IAAI,SAAS,SAAS;gBACpB,SAAS,MAAM,OAAO,OAAO;YAC/B,OAAO;gBACL,SAAS,MAAM,OAAO,OAAO,UAAU;YACzC;YAEA,IAAI,OAAO,KAAK,EAAE;gBAChB,SAAS,OAAO,KAAK,CAAC,OAAO,IAAI;YACnC,OAAO;gBACL;gBACA,aAAa;gBACb,SAAS;gBACT,YAAY;gBACZ,YAAY;YACd;QACF,EAAE,OAAO,KAAK;YACZ,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,YAAY;QAChB,SAAS;QACT,YAAY;QACZ,YAAY;QACZ,SAAS;IACX;IAEA,MAAM,aAAa;QACjB,QAAQ,SAAS,UAAU,WAAW;QACtC;IACF;IAEA,qBACE,6LAAC,4LAAM;QAAC,MAAM;QAAQ,SAAS;QAAS,WAAU;;0BAChD,6LAAC;gBAAI,WAAU;gBAA4B,eAAY;;;;;;0BAEvD,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,iMAAW;oBAAC,WAAU;;sCACrB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,iMAAW;oCAAC,WAAU;8CACpB,SAAS,UAAU,WAAW;;;;;;8CAEjC,6LAAC;oCACC,SAAS;oCACT,WAAU;8CAEV,cAAA,6LAAC,iOAAS;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAIzB,6LAAC;4BAAK,UAAU;4BAAc,WAAU;;gCACrC,uBACC,6LAAC;oCAAI,WAAU;8CACZ;;;;;;gCAIJ,SAAS,0BACR,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAW,WAAU;sDAA+C;;;;;;sDAGnF,6LAAC;4CACC,IAAG;4CACH,MAAK;4CACL,OAAO;4CACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;4CAC3C,QAAQ;4CACR,WAAU;4CACV,aAAY;;;;;;;;;;;;8CAKlB,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAQ,WAAU;sDAA+C;;;;;;sDAGhF,6LAAC;4CACC,IAAG;4CACH,MAAK;4CACL,OAAO;4CACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;4CACxC,QAAQ;4CACR,WAAU;4CACV,aAAY;;;;;;;;;;;;8CAIhB,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAW,WAAU;sDAA+C;;;;;;sDAGnF,6LAAC;4CACC,IAAG;4CACH,MAAK;4CACL,OAAO;4CACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;4CAC3C,QAAQ;4CACR,WAAW;4CACX,WAAU;4CACV,aAAY;;;;;;;;;;;;8CAIhB,6LAAC;oCACC,MAAK;oCACL,UAAU;oCACV,WAAW,AAAC,oFAGT,OAH4F,UAC3F,kCACA;8CAGH,UAAU,kBAAkB,SAAS,UAAU,WAAW;;;;;;8CAG7D,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCACC,MAAK;wCACL,SAAS;wCACT,WAAU;kDAET,SAAS,UACN,mCACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASpB;GAxJwB;;QAQK,6IAAO;;;KARZ", "debugId": null}}, {"offset": {"line": 285, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent3/agentepub/src/components/layout/Navbar.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport Link from 'next/link'\nimport { Bars3Icon, XMarkIcon } from '@heroicons/react/24/outline'\nimport { useAuth } from '@/contexts/AuthContext'\nimport AuthModal from '@/components/auth/AuthModal'\n\nexport default function Navbar() {\n  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)\n  const [authModalOpen, setAuthModalOpen] = useState(false)\n  const { user, signOut } = useAuth()\n\n  const navigation = [\n    { name: 'Como funciona', href: '#como-funciona' },\n    { name: 'Agent<PERSON>', href: '#agentes' },\n    { name: 'Preço', href: '#preco' },\n  ]\n\n  const handleAuthClick = () => {\n    if (user) {\n      // Redirect to dashboard\n      window.location.href = '/dashboard'\n    } else {\n      setAuthModalOpen(true)\n    }\n  }\n\n  const handleSignOut = async () => {\n    await signOut()\n  }\n\n  return (\n    <>\n      <header className=\"bg-white shadow-sm sticky top-0 z-40\">\n        <nav className=\"mx-auto flex max-w-7xl items-center justify-between p-6 lg:px-8\" aria-label=\"Global\">\n          <div className=\"flex lg:flex-1\">\n            <Link href=\"/\" className=\"-m-1.5 p-1.5\">\n              <span className=\"text-2xl font-bold text-primary-600\">AgentePub</span>\n            </Link>\n          </div>\n          \n          <div className=\"flex lg:hidden\">\n            <button\n              type=\"button\"\n              className=\"-m-2.5 inline-flex items-center justify-center rounded-md p-2.5 text-gray-700\"\n              onClick={() => setMobileMenuOpen(true)}\n            >\n              <span className=\"sr-only\">Abrir menu principal</span>\n              <Bars3Icon className=\"h-6 w-6\" aria-hidden=\"true\" />\n            </button>\n          </div>\n          \n          <div className=\"hidden lg:flex lg:gap-x-12\">\n            {navigation.map((item) => (\n              <a\n                key={item.name}\n                href={item.href}\n                className=\"text-sm font-semibold leading-6 text-gray-900 hover:text-primary-600 transition-colors\"\n              >\n                {item.name}\n              </a>\n            ))}\n          </div>\n          \n          <div className=\"hidden lg:flex lg:flex-1 lg:justify-end lg:gap-x-4\">\n            {user ? (\n              <div className=\"flex items-center gap-x-4\">\n                <span className=\"text-sm text-gray-700\">Olá, {user.email}</span>\n                <button\n                  onClick={handleAuthClick}\n                  className=\"text-sm font-semibold leading-6 text-primary-600 hover:text-primary-700 transition-colors\"\n                >\n                  Dashboard\n                </button>\n                <button\n                  onClick={handleSignOut}\n                  className=\"text-sm font-semibold leading-6 text-gray-600 hover:text-gray-700 transition-colors\"\n                >\n                  Sair\n                </button>\n              </div>\n            ) : (\n              <button\n                onClick={handleAuthClick}\n                className=\"text-sm font-semibold leading-6 text-gray-900 hover:text-primary-600 transition-colors\"\n              >\n                Entrar <span aria-hidden=\"true\">&rarr;</span>\n              </button>\n            )}\n          </div>\n        </nav>\n        \n        {/* Mobile menu */}\n        {mobileMenuOpen && (\n          <div className=\"lg:hidden\" role=\"dialog\" aria-modal=\"true\">\n            <div className=\"fixed inset-0 z-50\"></div>\n            <div className=\"fixed inset-y-0 right-0 z-50 w-full overflow-y-auto bg-white px-6 py-6 sm:max-w-sm sm:ring-1 sm:ring-gray-900/10\">\n              <div className=\"flex items-center justify-between\">\n                <Link href=\"/\" className=\"-m-1.5 p-1.5\">\n                  <span className=\"text-2xl font-bold text-primary-600\">AgentePub</span>\n                </Link>\n                <button\n                  type=\"button\"\n                  className=\"-m-2.5 rounded-md p-2.5 text-gray-700\"\n                  onClick={() => setMobileMenuOpen(false)}\n                >\n                  <span className=\"sr-only\">Fechar menu</span>\n                  <XMarkIcon className=\"h-6 w-6\" aria-hidden=\"true\" />\n                </button>\n              </div>\n              <div className=\"mt-6 flow-root\">\n                <div className=\"-my-6 divide-y divide-gray-500/10\">\n                  <div className=\"space-y-2 py-6\">\n                    {navigation.map((item) => (\n                      <a\n                        key={item.name}\n                        href={item.href}\n                        className=\"-mx-3 block rounded-lg px-3 py-2 text-base font-semibold leading-7 text-gray-900 hover:bg-gray-50\"\n                        onClick={() => setMobileMenuOpen(false)}\n                      >\n                        {item.name}\n                      </a>\n                    ))}\n                  </div>\n                  <div className=\"py-6\">\n                    {user ? (\n                      <div className=\"space-y-2\">\n                        <div className=\"text-sm text-gray-700 px-3\">Olá, {user.email}</div>\n                        <button\n                          onClick={() => {\n                            handleAuthClick()\n                            setMobileMenuOpen(false)\n                          }}\n                          className=\"-mx-3 block rounded-lg px-3 py-2.5 text-base font-semibold leading-7 text-primary-600 hover:bg-gray-50 w-full text-left\"\n                        >\n                          Dashboard\n                        </button>\n                        <button\n                          onClick={() => {\n                            handleSignOut()\n                            setMobileMenuOpen(false)\n                          }}\n                          className=\"-mx-3 block rounded-lg px-3 py-2.5 text-base font-semibold leading-7 text-gray-600 hover:bg-gray-50 w-full text-left\"\n                        >\n                          Sair\n                        </button>\n                      </div>\n                    ) : (\n                      <button\n                        onClick={() => {\n                          handleAuthClick()\n                          setMobileMenuOpen(false)\n                        }}\n                        className=\"-mx-3 block rounded-lg px-3 py-2.5 text-base font-semibold leading-7 text-gray-900 hover:bg-gray-50 w-full text-left\"\n                      >\n                        Entrar\n                      </button>\n                    )}\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n      </header>\n\n      <AuthModal \n        isOpen={authModalOpen} \n        onClose={() => setAuthModalOpen(false)} \n      />\n    </>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAAA;AACA;AACA;;;AANA;;;;;;AAQe,SAAS;;IACtB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,IAAA,yKAAQ,EAAC;IACrD,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,yKAAQ,EAAC;IACnD,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,IAAA,6IAAO;IAEjC,MAAM,aAAa;QACjB;YAAE,MAAM;YAAiB,MAAM;QAAiB;QAChD;YAAE,MAAM;YAAW,MAAM;QAAW;QACpC;YAAE,MAAM;YAAS,MAAM;QAAS;KACjC;IAED,MAAM,kBAAkB;QACtB,IAAI,MAAM;YACR,wBAAwB;YACxB,OAAO,QAAQ,CAAC,IAAI,GAAG;QACzB,OAAO;YACL,iBAAiB;QACnB;IACF;IAEA,MAAM,gBAAgB;QACpB,MAAM;IACR;IAEA,qBACE;;0BACE,6LAAC;gBAAO,WAAU;;kCAChB,6LAAC;wBAAI,WAAU;wBAAkE,cAAW;;0CAC1F,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,0KAAI;oCAAC,MAAK;oCAAI,WAAU;8CACvB,cAAA,6LAAC;wCAAK,WAAU;kDAAsC;;;;;;;;;;;;;;;;0CAI1D,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,kBAAkB;;sDAEjC,6LAAC;4CAAK,WAAU;sDAAU;;;;;;sDAC1B,6LAAC,iOAAS;4CAAC,WAAU;4CAAU,eAAY;;;;;;;;;;;;;;;;;0CAI/C,6LAAC;gCAAI,WAAU;0CACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC;wCAEC,MAAM,KAAK,IAAI;wCACf,WAAU;kDAET,KAAK,IAAI;uCAJL,KAAK,IAAI;;;;;;;;;;0CASpB,6LAAC;gCAAI,WAAU;0CACZ,qBACC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;;gDAAwB;gDAAM,KAAK,KAAK;;;;;;;sDACxD,6LAAC;4CACC,SAAS;4CACT,WAAU;sDACX;;;;;;sDAGD,6LAAC;4CACC,SAAS;4CACT,WAAU;sDACX;;;;;;;;;;;yDAKH,6LAAC;oCACC,SAAS;oCACT,WAAU;;wCACX;sDACQ,6LAAC;4CAAK,eAAY;sDAAO;;;;;;;;;;;;;;;;;;;;;;;oBAOvC,gCACC,6LAAC;wBAAI,WAAU;wBAAY,MAAK;wBAAS,cAAW;;0CAClD,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,0KAAI;gDAAC,MAAK;gDAAI,WAAU;0DACvB,cAAA,6LAAC;oDAAK,WAAU;8DAAsC;;;;;;;;;;;0DAExD,6LAAC;gDACC,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,kBAAkB;;kEAEjC,6LAAC;wDAAK,WAAU;kEAAU;;;;;;kEAC1B,6LAAC,iOAAS;wDAAC,WAAU;wDAAU,eAAY;;;;;;;;;;;;;;;;;;kDAG/C,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC;4DAEC,MAAM,KAAK,IAAI;4DACf,WAAU;4DACV,SAAS,IAAM,kBAAkB;sEAEhC,KAAK,IAAI;2DALL,KAAK,IAAI;;;;;;;;;;8DASpB,6LAAC;oDAAI,WAAU;8DACZ,qBACC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;oEAA6B;oEAAM,KAAK,KAAK;;;;;;;0EAC5D,6LAAC;gEACC,SAAS;oEACP;oEACA,kBAAkB;gEACpB;gEACA,WAAU;0EACX;;;;;;0EAGD,6LAAC;gEACC,SAAS;oEACP;oEACA,kBAAkB;gEACpB;gEACA,WAAU;0EACX;;;;;;;;;;;6EAKH,6LAAC;wDACC,SAAS;4DACP;4DACA,kBAAkB;wDACpB;wDACA,WAAU;kEACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAYjB,6LAAC,qJAAS;gBACR,QAAQ;gBACR,SAAS,IAAM,iBAAiB;;;;;;;;AAIxC;GArKwB;;QAGI,6IAAO;;;KAHX", "debugId": null}}, {"offset": {"line": 691, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent3/agentepub/src/components/home/<USER>"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { ChevronRightIcon, PlayIcon } from '@heroicons/react/24/outline'\nimport { useAuth } from '@/contexts/AuthContext'\nimport AuthModal from '@/components/auth/AuthModal'\n\nexport default function HeroSection() {\n  const [authModalOpen, setAuthModalOpen] = useState(false)\n  const { user } = useAuth()\n\n  const handleTestClick = () => {\n    if (user) {\n      window.location.href = '/teste'\n    } else {\n      setAuthModalOpen(true)\n    }\n  }\n\n  return (\n    <>\n      <div className=\"relative isolate px-6 pt-14 lg:px-8\">\n        <div\n          className=\"absolute inset-x-0 -top-40 -z-10 transform-gpu overflow-hidden blur-3xl sm:-top-80\"\n          aria-hidden=\"true\"\n        >\n          <div\n            className=\"relative left-[calc(50%-11rem)] aspect-[1155/678] w-[36.125rem] -translate-x-1/2 rotate-[30deg] bg-gradient-to-tr from-primary-400 to-primary-600 opacity-20 sm:left-[calc(50%-30rem)] sm:w-[72.1875rem]\"\n            style={{\n              clipPath:\n                'polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)',\n            }}\n          />\n        </div>\n        \n        <div className=\"mx-auto max-w-4xl py-32 sm:py-48 lg:py-56\">\n          <div className=\"hidden sm:mb-8 sm:flex sm:justify-center\">\n            <div className=\"relative rounded-full px-3 py-1 text-sm leading-6 text-gray-600 ring-1 ring-gray-900/10 hover:ring-gray-900/20 transition-all\">\n              Revolucione sua agência com IA.{' '}\n              <a href=\"#agentes\" className=\"font-semibold text-primary-600\">\n                <span className=\"absolute inset-0\" aria-hidden=\"true\" />\n                Conheça os agentes <span aria-hidden=\"true\">&rarr;</span>\n              </a>\n            </div>\n          </div>\n          \n          <div className=\"text-center\">\n            <h1 className=\"text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl\">\n              Trabalho publicitário{' '}\n              <span className=\"text-primary-600\">pronto em minutos</span>\n            </h1>\n            <p className=\"mt-6 text-lg leading-8 text-gray-600 max-w-2xl mx-auto\">\n              Agentes inteligentes que transformam briefings em estratégias, organizam prazos e otimizam campanhas. \n              Para equipes de Atendimento, Planejamento e Mídia que querem mais produtividade.\n            </p>\n            \n            <div className=\"mt-10 flex items-center justify-center gap-x-6\">\n              <button\n                onClick={handleTestClick}\n                className=\"rounded-md bg-primary-600 px-6 py-3 text-base font-semibold text-white shadow-sm hover:bg-primary-700 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-primary-600 transition-all transform hover:scale-105\"\n              >\n                Teste agora grátis\n              </button>\n              <a\n                href=\"#como-funciona\"\n                className=\"text-base font-semibold leading-6 text-gray-900 hover:text-primary-600 transition-colors flex items-center gap-1\"\n              >\n                Como funciona <ChevronRightIcon className=\"h-4 w-4\" />\n              </a>\n            </div>\n\n            {/* Stats */}\n            <div className=\"mt-16 grid grid-cols-1 gap-8 sm:grid-cols-3 lg:gap-16\">\n              <div className=\"text-center\">\n                <div className=\"text-3xl font-bold text-primary-600\">90%</div>\n                <div className=\"text-sm text-gray-600 mt-1\">Redução no tempo de briefing</div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-3xl font-bold text-primary-600\">3x</div>\n                <div className=\"text-sm text-gray-600 mt-1\">Mais campanhas organizadas</div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-3xl font-bold text-primary-600\">R$ 9,99</div>\n                <div className=\"text-sm text-gray-600 mt-1\">Por mês, por usuário</div>\n              </div>\n            </div>\n          </div>\n        </div>\n        \n        <div\n          className=\"absolute inset-x-0 top-[calc(100%-13rem)] -z-10 transform-gpu overflow-hidden blur-3xl sm:top-[calc(100%-30rem)]\"\n          aria-hidden=\"true\"\n        >\n          <div\n            className=\"relative left-[calc(50%+3rem)] aspect-[1155/678] w-[36.125rem] -translate-x-1/2 bg-gradient-to-tr from-primary-400 to-primary-600 opacity-20 sm:left-[calc(50%+36rem)] sm:w-[72.1875rem]\"\n            style={{\n              clipPath:\n                'polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)',\n            }}\n          />\n        </div>\n      </div>\n\n      <AuthModal \n        isOpen={authModalOpen} \n        onClose={() => setAuthModalOpen(false)} \n      />\n    </>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAOe,SAAS;;IACtB,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,yKAAQ,EAAC;IACnD,MAAM,EAAE,IAAI,EAAE,GAAG,IAAA,6IAAO;IAExB,MAAM,kBAAkB;QACtB,IAAI,MAAM;YACR,OAAO,QAAQ,CAAC,IAAI,GAAG;QACzB,OAAO;YACL,iBAAiB;QACnB;IACF;IAEA,qBACE;;0BACE,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,WAAU;wBACV,eAAY;kCAEZ,cAAA,6LAAC;4BACC,WAAU;4BACV,OAAO;gCACL,UACE;4BACJ;;;;;;;;;;;kCAIJ,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;wCAAgI;wCAC7G;sDAChC,6LAAC;4CAAE,MAAK;4CAAW,WAAU;;8DAC3B,6LAAC;oDAAK,WAAU;oDAAmB,eAAY;;;;;;gDAAS;8DACrC,6LAAC;oDAAK,eAAY;8DAAO;;;;;;;;;;;;;;;;;;;;;;;0CAKlD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;;4CAA8D;4CACpD;0DACtB,6LAAC;gDAAK,WAAU;0DAAmB;;;;;;;;;;;;kDAErC,6LAAC;wCAAE,WAAU;kDAAyD;;;;;;kDAKtE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,SAAS;gDACT,WAAU;0DACX;;;;;;0DAGD,6LAAC;gDACC,MAAK;gDACL,WAAU;;oDACX;kEACe,6LAAC,sPAAgB;wDAAC,WAAU;;;;;;;;;;;;;;;;;;kDAK9C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAsC;;;;;;kEACrD,6LAAC;wDAAI,WAAU;kEAA6B;;;;;;;;;;;;0DAE9C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAsC;;;;;;kEACrD,6LAAC;wDAAI,WAAU;kEAA6B;;;;;;;;;;;;0DAE9C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAsC;;;;;;kEACrD,6LAAC;wDAAI,WAAU;kEAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAMpD,6LAAC;wBACC,WAAU;wBACV,eAAY;kCAEZ,cAAA,6LAAC;4BACC,WAAU;4BACV,OAAO;gCACL,UACE;4BACJ;;;;;;;;;;;;;;;;;0BAKN,6LAAC,qJAAS;gBACR,QAAQ;gBACR,SAAS,IAAM,iBAAiB;;;;;;;;AAIxC;GAtGwB;;QAEL,6IAAO;;;KAFF", "debugId": null}}, {"offset": {"line": 1001, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent3/agentepub/src/components/home/<USER>"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { CheckIcon } from '@heroicons/react/24/outline'\nimport { useAuth } from '@/contexts/AuthContext'\nimport AuthModal from '@/components/auth/AuthModal'\n\nconst features = [\n  'Acesso aos 3 agentes inteligentes',\n  'Campanhas ilimitadas',\n  'Chat interno da equipe',\n  'Relatórios automatizados',\n  'Armazenamento de materiais',\n  'Suporte por email',\n  'Atualizações automáticas',\n  'Integração com ferramentas'\n]\n\nexport default function PricingSection() {\n  const [authModalOpen, setAuthModalOpen] = useState(false)\n  const { user } = useAuth()\n\n  const handleSubscribeClick = () => {\n    if (user) {\n      window.location.href = '/dashboard?subscribe=true'\n    } else {\n      setAuthModalOpen(true)\n    }\n  }\n\n  return (\n    <>\n      <div id=\"preco\" className=\"py-24 sm:py-32 bg-gray-50\">\n        <div className=\"mx-auto max-w-7xl px-6 lg:px-8\">\n          <div className=\"mx-auto max-w-2xl text-center\">\n            <h2 className=\"text-base font-semibold leading-7 text-primary-600\">Preço</h2>\n            <p className=\"mt-2 text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl\">\n              Simples e transparente\n            </p>\n            <p className=\"mt-6 text-lg leading-8 text-gray-600\">\n              Um preço justo para revolucionar sua agência. Sem pegadinhas, sem taxas ocultas.\n            </p>\n          </div>\n          \n          <div className=\"mx-auto mt-16 max-w-lg\">\n            <div className=\"rounded-3xl bg-white p-8 shadow-2xl ring-1 ring-gray-200\">\n              <div className=\"text-center\">\n                <h3 className=\"text-2xl font-bold text-gray-900\">Plano Único</h3>\n                <p className=\"mt-4 text-gray-600\">Tudo que você precisa para transformar sua agência</p>\n                \n                <div className=\"mt-8\">\n                  <div className=\"flex items-center justify-center\">\n                    <span className=\"text-5xl font-bold text-gray-900\">R$ 9,99</span>\n                    <span className=\"text-xl text-gray-600 ml-2\">/mês</span>\n                  </div>\n                  <p className=\"text-sm text-gray-500 mt-2\">por usuário</p>\n                </div>\n                \n                <button\n                  onClick={handleSubscribeClick}\n                  className=\"mt-8 w-full rounded-md bg-primary-600 px-6 py-3 text-base font-semibold text-white shadow-sm hover:bg-primary-700 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-primary-600 transition-all transform hover:scale-105\"\n                >\n                  Assinar agora\n                </button>\n                \n                <p className=\"text-xs text-gray-500 mt-4\">\n                  Cancele a qualquer momento. Sem fidelidade.\n                </p>\n              </div>\n              \n              <div className=\"mt-8\">\n                <h4 className=\"text-lg font-semibold text-gray-900 mb-4\">Tudo incluído:</h4>\n                <ul className=\"space-y-3\">\n                  {features.map((feature) => (\n                    <li key={feature} className=\"flex items-center gap-3\">\n                      <CheckIcon className=\"h-5 w-5 text-green-500 flex-shrink-0\" />\n                      <span className=\"text-gray-700\">{feature}</span>\n                    </li>\n                  ))}\n                </ul>\n              </div>\n            </div>\n          </div>\n\n          {/* Value proposition */}\n          <div className=\"mx-auto mt-16 max-w-4xl\">\n            <div className=\"text-center\">\n              <h3 className=\"text-xl font-semibold text-gray-900 mb-8\">Compare com os custos atuais</h3>\n            </div>\n            \n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n              <div className=\"text-center p-6 bg-white rounded-lg shadow\">\n                <div className=\"text-2xl font-bold text-red-600 mb-2\">R$ 3.000+</div>\n                <div className=\"text-sm text-gray-600\">Freelancer especializado por mês</div>\n              </div>\n              \n              <div className=\"text-center p-6 bg-white rounded-lg shadow\">\n                <div className=\"text-2xl font-bold text-red-600 mb-2\">R$ 8.000+</div>\n                <div className=\"text-sm text-gray-600\">Funcionário júnior por mês</div>\n              </div>\n              \n              <div className=\"text-center p-6 bg-primary-50 rounded-lg shadow ring-2 ring-primary-200\">\n                <div className=\"text-2xl font-bold text-primary-600 mb-2\">R$ 9,99</div>\n                <div className=\"text-sm text-gray-600\">AgentePub por mês</div>\n                <div className=\"text-xs text-primary-600 mt-1 font-medium\">300x mais barato!</div>\n              </div>\n            </div>\n          </div>\n\n          {/* FAQ preview */}\n          <div className=\"mx-auto mt-16 max-w-2xl text-center\">\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Dúvidas frequentes</h3>\n            <div className=\"space-y-4 text-left\">\n              <details className=\"bg-white p-4 rounded-lg shadow\">\n                <summary className=\"font-medium text-gray-900 cursor-pointer\">\n                  Posso cancelar a qualquer momento?\n                </summary>\n                <p className=\"mt-2 text-gray-600 text-sm\">\n                  Sim! Não há fidelidade. Você pode cancelar sua assinatura a qualquer momento e continuar usando até o final do período pago.\n                </p>\n              </details>\n              \n              <details className=\"bg-white p-4 rounded-lg shadow\">\n                <summary className=\"font-medium text-gray-900 cursor-pointer\">\n                  Quantos usuários posso adicionar?\n                </summary>\n                <p className=\"mt-2 text-gray-600 text-sm\">\n                  Ilimitados! Cada usuário paga R$ 9,99/mês. Você pode adicionar e remover usuários conforme necessário.\n                </p>\n              </details>\n              \n              <details className=\"bg-white p-4 rounded-lg shadow\">\n                <summary className=\"font-medium text-gray-900 cursor-pointer\">\n                  Os dados ficam seguros?\n                </summary>\n                <p className=\"mt-2 text-gray-600 text-sm\">\n                  Absolutamente. Usamos criptografia de ponta e seguimos as melhores práticas de segurança. Seus dados nunca são compartilhados.\n                </p>\n              </details>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <AuthModal \n        isOpen={authModalOpen} \n        onClose={() => setAuthModalOpen(false)} \n      />\n    </>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAOA,MAAM,WAAW;IACf;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAEc,SAAS;;IACtB,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,yKAAQ,EAAC;IACnD,MAAM,EAAE,IAAI,EAAE,GAAG,IAAA,6IAAO;IAExB,MAAM,uBAAuB;QAC3B,IAAI,MAAM;YACR,OAAO,QAAQ,CAAC,IAAI,GAAG;QACzB,OAAO;YACL,iBAAiB;QACnB;IACF;IAEA,qBACE;;0BACE,6LAAC;gBAAI,IAAG;gBAAQ,WAAU;0BACxB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAqD;;;;;;8CACnE,6LAAC;oCAAE,WAAU;8CAAmE;;;;;;8CAGhF,6LAAC;oCAAE,WAAU;8CAAuC;;;;;;;;;;;;sCAKtD,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAmC;;;;;;0DACjD,6LAAC;gDAAE,WAAU;0DAAqB;;;;;;0DAElC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAAmC;;;;;;0EACnD,6LAAC;gEAAK,WAAU;0EAA6B;;;;;;;;;;;;kEAE/C,6LAAC;wDAAE,WAAU;kEAA6B;;;;;;;;;;;;0DAG5C,6LAAC;gDACC,SAAS;gDACT,WAAU;0DACX;;;;;;0DAID,6LAAC;gDAAE,WAAU;0DAA6B;;;;;;;;;;;;kDAK5C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAA2C;;;;;;0DACzD,6LAAC;gDAAG,WAAU;0DACX,SAAS,GAAG,CAAC,CAAC,wBACb,6LAAC;wDAAiB,WAAU;;0EAC1B,6LAAC,iOAAS;gEAAC,WAAU;;;;;;0EACrB,6LAAC;gEAAK,WAAU;0EAAiB;;;;;;;uDAF1B;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAWnB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAG,WAAU;kDAA2C;;;;;;;;;;;8CAG3D,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAuC;;;;;;8DACtD,6LAAC;oDAAI,WAAU;8DAAwB;;;;;;;;;;;;sDAGzC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAuC;;;;;;8DACtD,6LAAC;oDAAI,WAAU;8DAAwB;;;;;;;;;;;;sDAGzC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAA2C;;;;;;8DAC1D,6LAAC;oDAAI,WAAU;8DAAwB;;;;;;8DACvC,6LAAC;oDAAI,WAAU;8DAA4C;;;;;;;;;;;;;;;;;;;;;;;;sCAMjE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA2C;;;;;;8CACzD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAQ,WAAU;;8DACjB,6LAAC;oDAAQ,WAAU;8DAA2C;;;;;;8DAG9D,6LAAC;oDAAE,WAAU;8DAA6B;;;;;;;;;;;;sDAK5C,6LAAC;4CAAQ,WAAU;;8DACjB,6LAAC;oDAAQ,WAAU;8DAA2C;;;;;;8DAG9D,6LAAC;oDAAE,WAAU;8DAA6B;;;;;;;;;;;;sDAK5C,6LAAC;4CAAQ,WAAU;;8DACjB,6LAAC;oDAAQ,WAAU;8DAA2C;;;;;;8DAG9D,6LAAC;oDAAE,WAAU;8DAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASpD,6LAAC,qJAAS;gBACR,QAAQ;gBACR,SAAS,IAAM,iBAAiB;;;;;;;;AAIxC;GApIwB;;QAEL,6IAAO;;;KAFF", "debugId": null}}]}