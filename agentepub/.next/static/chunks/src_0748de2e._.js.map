{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent3/agentepub/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey)\n\nexport type Database = {\n  public: {\n    Tables: {\n      profiles: {\n        Row: {\n          id: string\n          email: string\n          full_name: string | null\n          area: 'atendimento' | 'planejamento' | 'midia' | null\n          team_id: string | null\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id: string\n          email: string\n          full_name?: string | null\n          area?: 'atendimento' | 'planejamento' | 'midia' | null\n          team_id?: string | null\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          email?: string\n          full_name?: string | null\n          area?: 'atendimento' | 'planejamento' | 'midia' | null\n          team_id?: string | null\n          created_at?: string\n          updated_at?: string\n        }\n      }\n      teams: {\n        Row: {\n          id: string\n          name: string\n          created_by: string\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          name: string\n          created_by: string\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          name?: string\n          created_by?: string\n          created_at?: string\n          updated_at?: string\n        }\n      }\n      campaigns: {\n        Row: {\n          id: string\n          name: string\n          client: string\n          team_id: string\n          created_by: string\n          status: 'active' | 'completed' | 'paused'\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          name: string\n          client: string\n          team_id: string\n          created_by: string\n          status?: 'active' | 'completed' | 'paused'\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          name?: string\n          client?: string\n          team_id?: string\n          created_by?: string\n          status?: 'active' | 'completed' | 'paused'\n          created_at?: string\n          updated_at?: string\n        }\n      }\n      materials: {\n        Row: {\n          id: string\n          campaign_id: string\n          agent_type: 'atendimento' | 'planejamento' | 'midia'\n          title: string\n          content: string\n          file_url: string | null\n          created_by: string\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          campaign_id: string\n          agent_type: 'atendimento' | 'planejamento' | 'midia'\n          title: string\n          content: string\n          file_url?: string | null\n          created_by: string\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          campaign_id?: string\n          agent_type?: 'atendimento' | 'planejamento' | 'midia'\n          title?: string\n          content?: string\n          file_url?: string | null\n          created_by?: string\n          created_at?: string\n          updated_at?: string\n        }\n      }\n      chat_messages: {\n        Row: {\n          id: string\n          team_id: string\n          user_id: string\n          message: string\n          created_at: string\n        }\n        Insert: {\n          id?: string\n          team_id: string\n          user_id: string\n          message: string\n          created_at?: string\n        }\n        Update: {\n          id?: string\n          team_id?: string\n          user_id?: string\n          message?: string\n          created_at?: string\n        }\n      }\n    }\n  }\n}\n"], "names": [], "mappings": ";;;;AAEoB;AAFpB;;AAEA,MAAM;AACN,MAAM;AAEC,MAAM,WAAW,IAAA,0MAAY,EAAC,aAAa", "debugId": null}}, {"offset": {"line": 21, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent3/agentepub/src/contexts/AuthContext.tsx"], "sourcesContent": ["'use client'\n\nimport { createContext, useContext, useEffect, useState } from 'react'\nimport { User, Session } from '@supabase/supabase-js'\nimport { supabase } from '@/lib/supabase'\nimport { Database } from '@/lib/supabase'\n\ntype Profile = Database['public']['Tables']['profiles']['Row']\n\ninterface AuthContextType {\n  user: User | null\n  profile: Profile | null\n  session: Session | null\n  loading: boolean\n  signUp: (email: string, password: string, fullName: string) => Promise<{ error: any }>\n  signIn: (email: string, password: string) => Promise<{ error: any }>\n  signOut: () => Promise<void>\n  updateProfile: (updates: Partial<Profile>) => Promise<{ error: any }>\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined)\n\nexport function AuthProvider({ children }: { children: React.ReactNode }) {\n  const [user, setUser] = useState<User | null>(null)\n  const [profile, setProfile] = useState<Profile | null>(null)\n  const [session, setSession] = useState<Session | null>(null)\n  const [loading, setLoading] = useState(true)\n\n  useEffect(() => {\n    // Get initial session\n    supabase.auth.getSession().then(({ data: { session } }) => {\n      setSession(session)\n      setUser(session?.user ?? null)\n      if (session?.user) {\n        fetchProfile(session.user.id)\n      } else {\n        setLoading(false)\n      }\n    })\n\n    // Listen for auth changes\n    const {\n      data: { subscription },\n    } = supabase.auth.onAuthStateChange(async (event, session) => {\n      setSession(session)\n      setUser(session?.user ?? null)\n\n      if (session?.user) {\n        await fetchProfile(session.user.id)\n      } else {\n        setProfile(null)\n        setLoading(false)\n      }\n    })\n\n    return () => subscription.unsubscribe()\n  }, [])\n\n  const fetchProfile = async (userId: string) => {\n    try {\n      const { data, error } = await supabase\n        .from('profiles')\n        .select('*')\n        .eq('id', userId)\n        .single()\n\n      if (error) {\n        // If profile doesn't exist (PGRST116) or table doesn't exist (42P01)\n        if (error.code === 'PGRST116' || error.code === '42P01') {\n          console.log('Profile not found or table does not exist. User can still use the app.')\n          setProfile(null)\n        } else {\n          console.warn('Error fetching profile (non-critical):', error.message)\n          setProfile(null)\n        }\n      } else {\n        setProfile(data)\n      }\n    } catch (error) {\n      console.warn('Error fetching profile (non-critical):', error)\n      setProfile(null)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const signUp = async (email: string, password: string, fullName: string) => {\n    try {\n      console.log('📝 Attempting to sign up with:', email, fullName)\n\n      const { data, error } = await supabase.auth.signUp({\n        email,\n        password,\n        options: {\n          data: {\n            full_name: fullName,\n          },\n        },\n      })\n\n      if (error) {\n        console.error('❌ Sign up error:', error)\n        return { error }\n      }\n\n      console.log('✅ Sign up successful:', data.user?.email)\n\n      // If signup successful and user is confirmed, create profile manually\n      if (!error && data.user && !data.user.email_confirmed_at) {\n        // For email confirmation flow, profile will be created when user confirms\n        console.log('📧 User signed up, waiting for email confirmation')\n      } else if (!error && data.user && data.user.email_confirmed_at) {\n        // User is immediately confirmed, create profile\n        console.log('👤 Creating profile for confirmed user')\n        await createProfileIfNotExists(data.user.id, email, fullName)\n      }\n\n      return { error: null }\n    } catch (error) {\n      console.error('❌ Sign up exception:', error)\n      return { error }\n    }\n  }\n\n  const createProfileIfNotExists = async (userId: string, email: string, fullName: string) => {\n    try {\n      const { error } = await supabase\n        .from('profiles')\n        .insert({\n          id: userId,\n          email: email,\n          full_name: fullName\n        })\n\n      if (error && error.code !== '23505') { // 23505 is unique violation (profile already exists)\n        console.error('Error creating profile:', error)\n      }\n    } catch (error) {\n      console.error('Error creating profile:', error)\n    }\n  }\n\n  const signIn = async (email: string, password: string) => {\n    try {\n      console.log('🔐 Attempting to sign in with:', email)\n\n      const { data, error } = await supabase.auth.signInWithPassword({\n        email,\n        password,\n      })\n\n      if (error) {\n        console.error('❌ Sign in error:', error)\n        return { error }\n      }\n\n      console.log('✅ Sign in successful:', data.user?.email)\n      return { error: null }\n    } catch (error) {\n      console.error('❌ Sign in exception:', error)\n      return { error }\n    }\n  }\n\n  const signOut = async () => {\n    await supabase.auth.signOut()\n  }\n\n  const updateProfile = async (updates: Partial<Profile>) => {\n    if (!user) return { error: 'No user logged in' }\n\n    try {\n      const { data, error } = await supabase\n        .from('profiles')\n        .update(updates)\n        .eq('id', user.id)\n        .select()\n        .single()\n\n      if (error) {\n        return { error }\n      }\n\n      setProfile(data)\n      return { error: null }\n    } catch (error) {\n      return { error }\n    }\n  }\n\n  const value = {\n    user,\n    profile,\n    session,\n    loading,\n    signUp,\n    signIn,\n    signOut,\n    updateProfile,\n  }\n\n  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>\n}\n\nexport function useAuth() {\n  const context = useContext(AuthContext)\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider')\n  }\n  return context\n}\n"], "names": [], "mappings": ";;;;;;;AAEA;AAEA;;;AAJA;;;AAoBA,MAAM,4BAAc,IAAA,8KAAa,EAA8B;AAExD,SAAS,aAAa,KAA2C;QAA3C,EAAE,QAAQ,EAAiC,GAA3C;;IAC3B,MAAM,CAAC,MAAM,QAAQ,GAAG,IAAA,yKAAQ,EAAc;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,yKAAQ,EAAiB;IACvD,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,yKAAQ,EAAiB;IACvD,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,yKAAQ,EAAC;IAEvC,IAAA,0KAAS;kCAAC;YACR,sBAAsB;YACtB,qIAAQ,CAAC,IAAI,CAAC,UAAU,GAAG,IAAI;0CAAC;wBAAC,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE;oBACpD,WAAW;wBACH;oBAAR,QAAQ,CAAA,gBAAA,oBAAA,8BAAA,QAAS,IAAI,cAAb,2BAAA,gBAAiB;oBACzB,IAAI,oBAAA,8BAAA,QAAS,IAAI,EAAE;wBACjB,aAAa,QAAQ,IAAI,CAAC,EAAE;oBAC9B,OAAO;wBACL,WAAW;oBACb;gBACF;;YAEA,0BAA0B;YAC1B,MAAM,EACJ,MAAM,EAAE,YAAY,EAAE,EACvB,GAAG,qIAAQ,CAAC,IAAI,CAAC,iBAAiB;0CAAC,OAAO,OAAO;oBAChD,WAAW;wBACH;oBAAR,QAAQ,CAAA,gBAAA,oBAAA,8BAAA,QAAS,IAAI,cAAb,2BAAA,gBAAiB;oBAEzB,IAAI,oBAAA,8BAAA,QAAS,IAAI,EAAE;wBACjB,MAAM,aAAa,QAAQ,IAAI,CAAC,EAAE;oBACpC,OAAO;wBACL,WAAW;wBACX,WAAW;oBACb;gBACF;;YAEA;0CAAO,IAAM,aAAa,WAAW;;QACvC;iCAAG,EAAE;IAEL,MAAM,eAAe,OAAO;QAC1B,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,qIAAQ,CACnC,IAAI,CAAC,YACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,QACT,MAAM;YAET,IAAI,OAAO;gBACT,qEAAqE;gBACrE,IAAI,MAAM,IAAI,KAAK,cAAc,MAAM,IAAI,KAAK,SAAS;oBACvD,QAAQ,GAAG,CAAC;oBACZ,WAAW;gBACb,OAAO;oBACL,QAAQ,IAAI,CAAC,0CAA0C,MAAM,OAAO;oBACpE,WAAW;gBACb;YACF,OAAO;gBACL,WAAW;YACb;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,0CAA0C;YACvD,WAAW;QACb,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,SAAS,OAAO,OAAe,UAAkB;QACrD,IAAI;gBAkBmC;YAjBrC,QAAQ,GAAG,CAAC,kCAAkC,OAAO;YAErD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,qIAAQ,CAAC,IAAI,CAAC,MAAM,CAAC;gBACjD;gBACA;gBACA,SAAS;oBACP,MAAM;wBACJ,WAAW;oBACb;gBACF;YACF;YAEA,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,oBAAoB;gBAClC,OAAO;oBAAE;gBAAM;YACjB;YAEA,QAAQ,GAAG,CAAC,0BAAyB,aAAA,KAAK,IAAI,cAAT,iCAAA,WAAW,KAAK;YAErD,sEAAsE;YACtE,IAAI,CAAC,SAAS,KAAK,IAAI,IAAI,CAAC,KAAK,IAAI,CAAC,kBAAkB,EAAE;gBACxD,0EAA0E;gBAC1E,QAAQ,GAAG,CAAC;YACd,OAAO,IAAI,CAAC,SAAS,KAAK,IAAI,IAAI,KAAK,IAAI,CAAC,kBAAkB,EAAE;gBAC9D,gDAAgD;gBAChD,QAAQ,GAAG,CAAC;gBACZ,MAAM,yBAAyB,KAAK,IAAI,CAAC,EAAE,EAAE,OAAO;YACtD;YAEA,OAAO;gBAAE,OAAO;YAAK;QACvB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,OAAO;gBAAE;YAAM;QACjB;IACF;IAEA,MAAM,2BAA2B,OAAO,QAAgB,OAAe;QACrE,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,qIAAQ,CAC7B,IAAI,CAAC,YACL,MAAM,CAAC;gBACN,IAAI;gBACJ,OAAO;gBACP,WAAW;YACb;YAEF,IAAI,SAAS,MAAM,IAAI,KAAK,SAAS;gBACnC,QAAQ,KAAK,CAAC,2BAA2B;YAC3C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QAC3C;IACF;IAEA,MAAM,SAAS,OAAO,OAAe;QACnC,IAAI;gBAamC;YAZrC,QAAQ,GAAG,CAAC,kCAAkC;YAE9C,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,qIAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC;gBAC7D;gBACA;YACF;YAEA,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,oBAAoB;gBAClC,OAAO;oBAAE;gBAAM;YACjB;YAEA,QAAQ,GAAG,CAAC,0BAAyB,aAAA,KAAK,IAAI,cAAT,iCAAA,WAAW,KAAK;YACrD,OAAO;gBAAE,OAAO;YAAK;QACvB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,OAAO;gBAAE;YAAM;QACjB;IACF;IAEA,MAAM,UAAU;QACd,MAAM,qIAAQ,CAAC,IAAI,CAAC,OAAO;IAC7B;IAEA,MAAM,gBAAgB,OAAO;QAC3B,IAAI,CAAC,MAAM,OAAO;YAAE,OAAO;QAAoB;QAE/C,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,qIAAQ,CACnC,IAAI,CAAC,YACL,MAAM,CAAC,SACP,EAAE,CAAC,MAAM,KAAK,EAAE,EAChB,MAAM,GACN,MAAM;YAET,IAAI,OAAO;gBACT,OAAO;oBAAE;gBAAM;YACjB;YAEA,WAAW;YACX,OAAO;gBAAE,OAAO;YAAK;QACvB,EAAE,OAAO,OAAO;YACd,OAAO;gBAAE;YAAM;QACjB;IACF;IAEA,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBAAO,6LAAC,YAAY,QAAQ;QAAC,OAAO;kBAAQ;;;;;;AAC9C;GApLgB;KAAA;AAsLT,SAAS;;IACd,MAAM,UAAU,IAAA,2KAAU,EAAC;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANgB", "debugId": null}}]}