{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent3/agentepub/src/components/auth/ProtectedRoute.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { useAuth } from '@/contexts/AuthContext'\n\ninterface ProtectedRouteProps {\n  children: React.ReactNode\n  redirectTo?: string\n}\n\nexport default function ProtectedRoute({ children, redirectTo = '/' }: ProtectedRouteProps) {\n  const { user, loading } = useAuth()\n  const router = useRouter()\n\n  useEffect(() => {\n    if (!loading && !user) {\n      router.push(redirectTo)\n    }\n  }, [user, loading, router, redirectTo])\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600\"></div>\n      </div>\n    )\n  }\n\n  if (!user) {\n    return null\n  }\n\n  return <>{children}</>\n}\n\n// Loading component for better UX\nexport function LoadingSpinner({ size = 'md' }: { size?: 'sm' | 'md' | 'lg' }) {\n  const sizeClasses = {\n    sm: 'h-4 w-4',\n    md: 'h-8 w-8',\n    lg: 'h-12 w-12'\n  }\n\n  return (\n    <div className=\"flex items-center justify-center\">\n      <div className={`animate-spin rounded-full border-b-2 border-primary-600 ${sizeClasses[size]}`}></div>\n    </div>\n  )\n}\n\n// Auth guard hook for pages\nexport function useAuthGuard(redirectTo = '/') {\n  const { user, loading } = useAuth()\n  const router = useRouter()\n\n  useEffect(() => {\n    if (!loading && !user) {\n      router.push(redirectTo)\n    }\n  }, [user, loading, router, redirectTo])\n\n  return { user, loading, isAuthenticated: !!user }\n}\n"], "names": [], "mappings": ";;;;;;;;;AAEA;AACA;AACA;;;AAJA;;;;AAWe,SAAS,eAAe,KAAmD;QAAnD,EAAE,QAAQ,EAAE,aAAa,GAAG,EAAuB,GAAnD;;IACrC,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,IAAA,6IAAO;IACjC,MAAM,SAAS,IAAA,kJAAS;IAExB,IAAA,0KAAS;oCAAC;YACR,IAAI,CAAC,WAAW,CAAC,MAAM;gBACrB,OAAO,IAAI,CAAC;YACd;QACF;mCAAG;QAAC;QAAM;QAAS;QAAQ;KAAW;IAEtC,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,qBAAO;kBAAG;;AACZ;GAvBwB;;QACI,6IAAO;QAClB,kJAAS;;;KAFF;AA0BjB,SAAS,eAAe,KAA8C;QAA9C,EAAE,OAAO,IAAI,EAAiC,GAA9C;IAC7B,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAW,AAAC,2DAA4E,OAAlB,WAAW,CAAC,KAAK;;;;;;;;;;;AAGlG;MAZgB;AAeT,SAAS;QAAa,aAAA,iEAAa;;IACxC,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,IAAA,6IAAO;IACjC,MAAM,SAAS,IAAA,kJAAS;IAExB,IAAA,0KAAS;kCAAC;YACR,IAAI,CAAC,WAAW,CAAC,MAAM;gBACrB,OAAO,IAAI,CAAC;YACd;QACF;iCAAG;QAAC;QAAM;QAAS;QAAQ;KAAW;IAEtC,OAAO;QAAE;QAAM;QAAS,iBAAiB,CAAC,CAAC;IAAK;AAClD;IAXgB;;QACY,6IAAO;QAClB,kJAAS", "debugId": null}}, {"offset": {"line": 131, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent3/agentepub/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatDate(date: string | Date) {\n  return new Intl.DateTimeFormat('pt-BR', {\n    day: '2-digit',\n    month: '2-digit',\n    year: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit',\n  }).format(new Date(date))\n}\n\nexport function formatCurrency(amount: number) {\n  return new Intl.NumberFormat('pt-BR', {\n    style: 'currency',\n    currency: 'BRL',\n  }).format(amount)\n}\n\nexport function getAreaDisplayName(area: string) {\n  const areaNames = {\n    atendimento: 'Atendimento',\n    planejamento: 'Planejamento',\n    midia: 'Mídia'\n  }\n  return areaNames[area as keyof typeof areaNames] || area\n}\n\nexport function generateSlug(text: string) {\n  return text\n    .toLowerCase()\n    .normalize('NFD')\n    .replace(/[\\u0300-\\u036f]/g, '')\n    .replace(/[^a-z0-9\\s-]/g, '')\n    .replace(/\\s+/g, '-')\n    .replace(/-+/g, '-')\n    .trim()\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,IAAA,yKAAO,EAAC,IAAA,gJAAI,EAAC;AACtB;AAEO,SAAS,WAAW,IAAmB;IAC5C,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,KAAK;QACL,OAAO;QACP,MAAM;QACN,MAAM;QACN,QAAQ;IACV,GAAG,MAAM,CAAC,IAAI,KAAK;AACrB;AAEO,SAAS,eAAe,MAAc;IAC3C,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;IACZ,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,mBAAmB,IAAY;IAC7C,MAAM,YAAY;QAChB,aAAa;QACb,cAAc;QACd,OAAO;IACT;IACA,OAAO,SAAS,CAAC,KAA+B,IAAI;AACtD;AAEO,SAAS,aAAa,IAAY;IACvC,OAAO,KACJ,WAAW,GACX,SAAS,CAAC,OACV,OAAO,CAAC,oBAAoB,IAC5B,OAAO,CAAC,iBAAiB,IACzB,OAAO,CAAC,QAAQ,KAChB,OAAO,CAAC,OAAO,KACf,IAAI;AACT", "debugId": null}}, {"offset": {"line": 186, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent3/agentepub/src/components/dashboard/DashboardLayout.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { cn } from '@/lib/utils'\nimport {\n  Bars3Icon,\n  XMarkIcon,\n  HomeIcon,\n  FolderIcon,\n  UserGroupIcon,\n  DocumentChartBarIcon,\n  ChatBubbleLeftRightIcon,\n  Cog6ToothIcon,\n  ArrowRightOnRectangleIcon\n} from '@heroicons/react/24/outline'\n\nconst navigation = [\n  { name: 'Dashboard', href: '/dashboard', icon: HomeIcon },\n  { name: 'Campanhas', href: '/dashboard/campanhas', icon: FolderIcon },\n  { name: 'Agent<PERSON>', href: '/dashboard/agentes', icon: UserGroupIcon },\n  { name: '<PERSON><PERSON><PERSON><PERSON><PERSON>', href: '/dashboard/relatorios', icon: DocumentChartBarIcon },\n  { name: 'Chat', href: '/dashboard/chat', icon: ChatBubbleLeftRightIcon },\n  { name: 'Configura<PERSON>õ<PERSON>', href: '/dashboard/configuracoes', icon: Cog6ToothIcon },\n]\n\ninterface DashboardLayoutProps {\n  children: React.ReactNode\n}\n\nexport default function DashboardLayout({ children }: DashboardLayoutProps) {\n  const [sidebarOpen, setSidebarOpen] = useState(false)\n  const pathname = usePathname()\n  const { user, profile, signOut } = useAuth()\n\n  const handleSignOut = async () => {\n    await signOut()\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Mobile sidebar */}\n      {sidebarOpen && (\n        <div className=\"fixed inset-0 z-50 lg:hidden\">\n          <div className=\"fixed inset-0 bg-gray-600 bg-opacity-75\" onClick={() => setSidebarOpen(false)} />\n          <div className=\"fixed inset-y-0 left-0 flex w-full max-w-xs flex-col bg-white shadow-xl\">\n            <div className=\"flex h-16 items-center justify-between px-6 border-b border-gray-200\">\n              <Link href=\"/dashboard\" className=\"text-xl font-bold text-primary-600\">\n                AgentePub\n              </Link>\n              <button\n                onClick={() => setSidebarOpen(false)}\n                className=\"text-gray-400 hover:text-gray-600\"\n              >\n                <XMarkIcon className=\"h-6 w-6\" />\n              </button>\n            </div>\n            \n            <nav className=\"flex-1 px-6 py-6\">\n              <ul className=\"space-y-2\">\n                {navigation.map((item) => (\n                  <li key={item.name}>\n                    <Link\n                      href={item.href}\n                      className={cn(\n                        'flex items-center gap-3 px-3 py-2 rounded-md text-sm font-medium transition-colors',\n                        pathname === item.href\n                          ? 'bg-primary-100 text-primary-700'\n                          : 'text-gray-700 hover:bg-gray-100'\n                      )}\n                      onClick={() => setSidebarOpen(false)}\n                    >\n                      <item.icon className=\"h-5 w-5\" />\n                      {item.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </nav>\n\n            <div className=\"border-t border-gray-200 p-6\">\n              <div className=\"flex items-center gap-3 mb-4\">\n                <div className=\"h-8 w-8 bg-primary-100 rounded-full flex items-center justify-center\">\n                  <span className=\"text-sm font-medium text-primary-700\">\n                    {profile?.full_name?.charAt(0) || user?.email?.charAt(0) || 'U'}\n                  </span>\n                </div>\n                <div className=\"flex-1 min-w-0\">\n                  <p className=\"text-sm font-medium text-gray-900 truncate\">\n                    {profile?.full_name || user?.email}\n                  </p>\n                  {profile?.area && (\n                    <p className=\"text-xs text-gray-500 capitalize\">{profile.area}</p>\n                  )}\n                </div>\n              </div>\n              <button\n                onClick={handleSignOut}\n                className=\"flex items-center gap-2 text-sm text-gray-700 hover:text-gray-900 transition-colors\"\n              >\n                <ArrowRightOnRectangleIcon className=\"h-4 w-4\" />\n                Sair\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Desktop sidebar */}\n      <div className=\"hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col\">\n        <div className=\"flex flex-col flex-1 bg-white border-r border-gray-200\">\n          <div className=\"flex h-16 items-center px-6 border-b border-gray-200\">\n            <Link href=\"/dashboard\" className=\"text-xl font-bold text-primary-600\">\n              AgentePub\n            </Link>\n          </div>\n          \n          <nav className=\"flex-1 px-6 py-6\">\n            <ul className=\"space-y-2\">\n              {navigation.map((item) => (\n                <li key={item.name}>\n                  <Link\n                    href={item.href}\n                    className={cn(\n                      'flex items-center gap-3 px-3 py-2 rounded-md text-sm font-medium transition-colors',\n                      pathname === item.href\n                        ? 'bg-primary-100 text-primary-700'\n                        : 'text-gray-700 hover:bg-gray-100'\n                    )}\n                  >\n                    <item.icon className=\"h-5 w-5\" />\n                    {item.name}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </nav>\n\n          <div className=\"border-t border-gray-200 p-6\">\n            <div className=\"flex items-center gap-3 mb-4\">\n              <div className=\"h-8 w-8 bg-primary-100 rounded-full flex items-center justify-center\">\n                <span className=\"text-sm font-medium text-primary-700\">\n                  {profile?.full_name?.charAt(0) || user?.email?.charAt(0) || 'U'}\n                </span>\n              </div>\n              <div className=\"flex-1 min-w-0\">\n                <p className=\"text-sm font-medium text-gray-900 truncate\">\n                  {profile?.full_name || user?.email}\n                </p>\n                {profile?.area && (\n                  <p className=\"text-xs text-gray-500 capitalize\">{profile.area}</p>\n                )}\n              </div>\n            </div>\n            <button\n              onClick={handleSignOut}\n              className=\"flex items-center gap-2 text-sm text-gray-700 hover:text-gray-900 transition-colors\"\n            >\n              <ArrowRightOnRectangleIcon className=\"h-4 w-4\" />\n              Sair\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Main content */}\n      <div className=\"lg:pl-64\">\n        {/* Top bar */}\n        <div className=\"sticky top-0 z-40 bg-white border-b border-gray-200 px-6 py-4\">\n          <div className=\"flex items-center justify-between\">\n            <button\n              onClick={() => setSidebarOpen(true)}\n              className=\"lg:hidden text-gray-500 hover:text-gray-700\"\n            >\n              <Bars3Icon className=\"h-6 w-6\" />\n            </button>\n            \n            <div className=\"flex items-center gap-4\">\n              <div className=\"text-sm text-gray-500\">\n                {new Date().toLocaleDateString('pt-BR', { \n                  weekday: 'long', \n                  year: 'numeric', \n                  month: 'long', \n                  day: 'numeric' \n                })}\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Page content */}\n        <main className=\"px-6 py-8\">\n          {children}\n        </main>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAPA;;;;;;;AAmBA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAa,MAAM;QAAc,MAAM,8NAAQ;IAAC;IACxD;QAAE,MAAM;QAAa,MAAM;QAAwB,MAAM,oOAAU;IAAC;IACpE;QAAE,MAAM;QAAW,MAAM;QAAsB,MAAM,6OAAa;IAAC;IACnE;QAAE,MAAM;QAAc,MAAM;QAAyB,MAAM,kQAAoB;IAAC;IAChF;QAAE,MAAM;QAAQ,MAAM;QAAmB,MAAM,2QAAuB;IAAC;IACvE;QAAE,MAAM;QAAiB,MAAM;QAA4B,MAAM,6OAAa;IAAC;CAChF;AAMc,SAAS,gBAAgB,KAAkC;QAAlC,EAAE,QAAQ,EAAwB,GAAlC;QAsDnB,oBAAiC,aA0DnC,qBAAiC;;IA/GlD,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,yKAAQ,EAAC;IAC/C,MAAM,WAAW,IAAA,oJAAW;IAC5B,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,IAAA,6IAAO;IAE1C,MAAM,gBAAgB;QACpB,MAAM;IACR;IAEA,qBACE,6LAAC;QAAI,WAAU;;YAEZ,6BACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;wBAA0C,SAAS,IAAM,eAAe;;;;;;kCACvF,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,0KAAI;wCAAC,MAAK;wCAAa,WAAU;kDAAqC;;;;;;kDAGvE,6LAAC;wCACC,SAAS,IAAM,eAAe;wCAC9B,WAAU;kDAEV,cAAA,6LAAC,iOAAS;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAIzB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAG,WAAU;8CACX,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC;sDACC,cAAA,6LAAC,0KAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAW,IAAA,4HAAE,EACX,sFACA,aAAa,KAAK,IAAI,GAClB,oCACA;gDAEN,SAAS,IAAM,eAAe;;kEAE9B,6LAAC,KAAK,IAAI;wDAAC,WAAU;;;;;;oDACpB,KAAK,IAAI;;;;;;;2CAZL,KAAK,IAAI;;;;;;;;;;;;;;;0CAmBxB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAK,WAAU;8DACb,CAAA,oBAAA,+BAAA,qBAAA,QAAS,SAAS,cAAlB,yCAAA,mBAAoB,MAAM,CAAC,QAAM,iBAAA,4BAAA,cAAA,KAAM,KAAK,cAAX,kCAAA,YAAa,MAAM,CAAC,OAAM;;;;;;;;;;;0DAGhE,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEACV,CAAA,oBAAA,8BAAA,QAAS,SAAS,MAAI,iBAAA,2BAAA,KAAM,KAAK;;;;;;oDAEnC,CAAA,oBAAA,8BAAA,QAAS,IAAI,mBACZ,6LAAC;wDAAE,WAAU;kEAAoC,QAAQ,IAAI;;;;;;;;;;;;;;;;;;kDAInE,6LAAC;wCACC,SAAS;wCACT,WAAU;;0DAEV,6LAAC,iRAAyB;gDAAC,WAAU;;;;;;4CAAY;;;;;;;;;;;;;;;;;;;;;;;;;0BAS3D,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,0KAAI;gCAAC,MAAK;gCAAa,WAAU;0CAAqC;;;;;;;;;;;sCAKzE,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAG,WAAU;0CACX,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC;kDACC,cAAA,6LAAC,0KAAI;4CACH,MAAM,KAAK,IAAI;4CACf,WAAW,IAAA,4HAAE,EACX,sFACA,aAAa,KAAK,IAAI,GAClB,oCACA;;8DAGN,6LAAC,KAAK,IAAI;oDAAC,WAAU;;;;;;gDACpB,KAAK,IAAI;;;;;;;uCAXL,KAAK,IAAI;;;;;;;;;;;;;;;sCAkBxB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DACb,CAAA,oBAAA,+BAAA,sBAAA,QAAS,SAAS,cAAlB,0CAAA,oBAAoB,MAAM,CAAC,QAAM,iBAAA,4BAAA,eAAA,KAAM,KAAK,cAAX,mCAAA,aAAa,MAAM,CAAC,OAAM;;;;;;;;;;;sDAGhE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DACV,CAAA,oBAAA,8BAAA,QAAS,SAAS,MAAI,iBAAA,2BAAA,KAAM,KAAK;;;;;;gDAEnC,CAAA,oBAAA,8BAAA,QAAS,IAAI,mBACZ,6LAAC;oDAAE,WAAU;8DAAoC,QAAQ,IAAI;;;;;;;;;;;;;;;;;;8CAInE,6LAAC;oCACC,SAAS;oCACT,WAAU;;sDAEV,6LAAC,iRAAyB;4CAAC,WAAU;;;;;;wCAAY;;;;;;;;;;;;;;;;;;;;;;;;0BAQzD,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS,IAAM,eAAe;oCAC9B,WAAU;8CAEV,cAAA,6LAAC,iOAAS;wCAAC,WAAU;;;;;;;;;;;8CAGvB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;kDACZ,IAAI,OAAO,kBAAkB,CAAC,SAAS;4CACtC,SAAS;4CACT,MAAM;4CACN,OAAO;4CACP,KAAK;wCACP;;;;;;;;;;;;;;;;;;;;;;kCAOR,6LAAC;wBAAK,WAAU;kCACb;;;;;;;;;;;;;;;;;;AAKX;GAvKwB;;QAEL,oJAAW;QACO,6IAAO;;;KAHpB", "debugId": null}}, {"offset": {"line": 670, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent3/agentepub/src/app/dashboard/campanhas/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport ProtectedRoute from '@/components/auth/ProtectedRoute'\nimport DashboardLayout from '@/components/dashboard/DashboardLayout'\nimport { \n  PlusIcon, \n  FolderIcon,\n  CalendarDaysIcon,\n  UserIcon,\n  EllipsisVerticalIcon,\n  MagnifyingGlassIcon\n} from '@heroicons/react/24/outline'\n\n// Mock data - in real app this would come from Supabase\nconst mockCampaigns = [\n  {\n    id: '1',\n    name: 'Campanha Black Friday 2024',\n    client: 'E-commerce Fashion',\n    status: 'active' as const,\n    created_at: '2024-01-15',\n    materials_count: 8,\n    team_members: 3\n  },\n  {\n    id: '2',\n    name: 'Lançamento App Delivery',\n    client: 'FoodTech Startup',\n    status: 'active' as const,\n    created_at: '2024-01-10',\n    materials_count: 12,\n    team_members: 4\n  },\n  {\n    id: '3',\n    name: 'Campanha Institucional',\n    client: 'Construtora ABC',\n    status: 'completed' as const,\n    created_at: '2024-01-05',\n    materials_count: 15,\n    team_members: 2\n  }\n]\n\nexport default function CampanhasPage() {\n  const [campaigns] = useState(mockCampaigns)\n  const [searchTerm, setSearchTerm] = useState('')\n  const [showCreateModal, setShowCreateModal] = useState(false)\n  const [newCampaign, setNewCampaign] = useState({\n    name: '',\n    client: ''\n  })\n\n  const filteredCampaigns = campaigns.filter(campaign =>\n    campaign.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n    campaign.client.toLowerCase().includes(searchTerm.toLowerCase())\n  )\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'active':\n        return 'bg-green-100 text-green-800'\n      case 'completed':\n        return 'bg-gray-100 text-gray-800'\n      case 'paused':\n        return 'bg-yellow-100 text-yellow-800'\n      default:\n        return 'bg-gray-100 text-gray-800'\n    }\n  }\n\n  const getStatusText = (status: string) => {\n    switch (status) {\n      case 'active':\n        return 'Ativa'\n      case 'completed':\n        return 'Concluída'\n      case 'paused':\n        return 'Pausada'\n      default:\n        return status\n    }\n  }\n\n  const handleCreateCampaign = (e: React.FormEvent) => {\n    e.preventDefault()\n    // In real app, this would create the campaign in Supabase\n    console.log('Creating campaign:', newCampaign)\n    setShowCreateModal(false)\n    setNewCampaign({ name: '', client: '' })\n  }\n\n  return (\n    <ProtectedRoute>\n      <DashboardLayout>\n        <div className=\"space-y-6\">\n          {/* Header */}\n          <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\">\n            <div>\n              <h1 className=\"text-2xl font-bold text-gray-900\">Campanhas</h1>\n              <p className=\"text-gray-600\">Gerencie todas as suas campanhas em um só lugar</p>\n            </div>\n            <button\n              onClick={() => setShowCreateModal(true)}\n              className=\"bg-primary-600 text-white px-4 py-2 rounded-md font-medium hover:bg-primary-700 transition-colors flex items-center gap-2\"\n            >\n              <PlusIcon className=\"h-5 w-5\" />\n              Nova Campanha\n            </button>\n          </div>\n\n          {/* Search */}\n          <div className=\"relative\">\n            <MagnifyingGlassIcon className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400\" />\n            <input\n              type=\"text\"\n              placeholder=\"Buscar campanhas...\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n            />\n          </div>\n\n          {/* Campaigns Grid */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n            {filteredCampaigns.map((campaign) => (\n              <div key={campaign.id} className=\"bg-white rounded-lg shadow hover:shadow-md transition-shadow\">\n                <div className=\"p-6\">\n                  <div className=\"flex items-start justify-between mb-4\">\n                    <div className=\"flex items-center gap-3\">\n                      <div className=\"bg-primary-100 p-2 rounded-lg\">\n                        <FolderIcon className=\"h-6 w-6 text-primary-600\" />\n                      </div>\n                      <div>\n                        <h3 className=\"font-semibold text-gray-900 line-clamp-1\">{campaign.name}</h3>\n                        <p className=\"text-sm text-gray-600\">{campaign.client}</p>\n                      </div>\n                    </div>\n                    <button className=\"text-gray-400 hover:text-gray-600\">\n                      <EllipsisVerticalIcon className=\"h-5 w-5\" />\n                    </button>\n                  </div>\n\n                  <div className=\"space-y-3\">\n                    <div className=\"flex items-center justify-between\">\n                      <span className=\"text-sm text-gray-600\">Status</span>\n                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(campaign.status)}`}>\n                        {getStatusText(campaign.status)}\n                      </span>\n                    </div>\n\n                    <div className=\"flex items-center justify-between\">\n                      <span className=\"text-sm text-gray-600\">Materiais</span>\n                      <span className=\"text-sm font-medium text-gray-900\">{campaign.materials_count}</span>\n                    </div>\n\n                    <div className=\"flex items-center justify-between\">\n                      <span className=\"text-sm text-gray-600\">Equipe</span>\n                      <div className=\"flex items-center gap-1\">\n                        <UserIcon className=\"h-4 w-4 text-gray-400\" />\n                        <span className=\"text-sm font-medium text-gray-900\">{campaign.team_members}</span>\n                      </div>\n                    </div>\n\n                    <div className=\"flex items-center justify-between\">\n                      <span className=\"text-sm text-gray-600\">Criada em</span>\n                      <div className=\"flex items-center gap-1\">\n                        <CalendarDaysIcon className=\"h-4 w-4 text-gray-400\" />\n                        <span className=\"text-sm text-gray-900\">\n                          {new Date(campaign.created_at).toLocaleDateString('pt-BR')}\n                        </span>\n                      </div>\n                    </div>\n                  </div>\n\n                  <div className=\"mt-6 pt-4 border-t border-gray-200\">\n                    <button className=\"w-full bg-gray-50 text-gray-700 py-2 px-4 rounded-md font-medium hover:bg-gray-100 transition-colors\">\n                      Ver Detalhes\n                    </button>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n\n          {filteredCampaigns.length === 0 && (\n            <div className=\"text-center py-12\">\n              <FolderIcon className=\"mx-auto h-12 w-12 text-gray-400\" />\n              <h3 className=\"mt-2 text-sm font-medium text-gray-900\">Nenhuma campanha encontrada</h3>\n              <p className=\"mt-1 text-sm text-gray-500\">\n                {searchTerm ? 'Tente ajustar sua busca' : 'Comece criando sua primeira campanha'}\n              </p>\n              {!searchTerm && (\n                <div className=\"mt-6\">\n                  <button\n                    onClick={() => setShowCreateModal(true)}\n                    className=\"bg-primary-600 text-white px-4 py-2 rounded-md font-medium hover:bg-primary-700 transition-colors\"\n                  >\n                    Criar Primeira Campanha\n                  </button>\n                </div>\n              )}\n            </div>\n          )}\n        </div>\n\n        {/* Create Campaign Modal */}\n        {showCreateModal && (\n          <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\">\n            <div className=\"bg-white rounded-lg max-w-md w-full p-6\">\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Nova Campanha</h3>\n              \n              <form onSubmit={handleCreateCampaign} className=\"space-y-4\">\n                <div>\n                  <label htmlFor=\"name\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    Nome da Campanha\n                  </label>\n                  <input\n                    id=\"name\"\n                    type=\"text\"\n                    value={newCampaign.name}\n                    onChange={(e) => setNewCampaign({ ...newCampaign, name: e.target.value })}\n                    required\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n                    placeholder=\"Ex: Campanha Black Friday 2024\"\n                  />\n                </div>\n\n                <div>\n                  <label htmlFor=\"client\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    Cliente\n                  </label>\n                  <input\n                    id=\"client\"\n                    type=\"text\"\n                    value={newCampaign.client}\n                    onChange={(e) => setNewCampaign({ ...newCampaign, client: e.target.value })}\n                    required\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n                    placeholder=\"Ex: E-commerce Fashion\"\n                  />\n                </div>\n\n                <div className=\"flex gap-3 pt-4\">\n                  <button\n                    type=\"button\"\n                    onClick={() => setShowCreateModal(false)}\n                    className=\"flex-1 bg-gray-200 text-gray-800 py-2 px-4 rounded-md font-medium hover:bg-gray-300 transition-colors\"\n                  >\n                    Cancelar\n                  </button>\n                  <button\n                    type=\"submit\"\n                    className=\"flex-1 bg-primary-600 text-white py-2 px-4 rounded-md font-medium hover:bg-primary-700 transition-colors\"\n                  >\n                    Criar Campanha\n                  </button>\n                </div>\n              </form>\n            </div>\n          </div>\n        )}\n      </DashboardLayout>\n    </ProtectedRoute>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AALA;;;;;AAcA,wDAAwD;AACxD,MAAM,gBAAgB;IACpB;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,QAAQ;QACR,YAAY;QACZ,iBAAiB;QACjB,cAAc;IAChB;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,QAAQ;QACR,YAAY;QACZ,iBAAiB;QACjB,cAAc;IAChB;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,QAAQ;QACR,YAAY;QACZ,iBAAiB;QACjB,cAAc;IAChB;CACD;AAEc,SAAS;;IACtB,MAAM,CAAC,UAAU,GAAG,IAAA,yKAAQ,EAAC;IAC7B,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,yKAAQ,EAAC;IAC7C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,IAAA,yKAAQ,EAAC;IACvD,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,yKAAQ,EAAC;QAC7C,MAAM;QACN,QAAQ;IACV;IAEA,MAAM,oBAAoB,UAAU,MAAM,CAAC,CAAA,WACzC,SAAS,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC3D,SAAS,MAAM,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;IAG/D,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,EAAE,cAAc;QAChB,0DAA0D;QAC1D,QAAQ,GAAG,CAAC,sBAAsB;QAClC,mBAAmB;QACnB,eAAe;YAAE,MAAM;YAAI,QAAQ;QAAG;IACxC;IAEA,qBACE,6LAAC,0JAAc;kBACb,cAAA,6LAAC,gKAAe;;8BACd,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAmC;;;;;;sDACjD,6LAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAE/B,6LAAC;oCACC,SAAS,IAAM,mBAAmB;oCAClC,WAAU;;sDAEV,6LAAC,8NAAQ;4CAAC,WAAU;;;;;;wCAAY;;;;;;;;;;;;;sCAMpC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+PAAmB;oCAAC,WAAU;;;;;;8CAC/B,6LAAC;oCACC,MAAK;oCACL,aAAY;oCACZ,OAAO;oCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oCAC7C,WAAU;;;;;;;;;;;;sCAKd,6LAAC;4BAAI,WAAU;sCACZ,kBAAkB,GAAG,CAAC,CAAC,yBACtB,6LAAC;oCAAsB,WAAU;8CAC/B,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC,oOAAU;oEAAC,WAAU;;;;;;;;;;;0EAExB,6LAAC;;kFACC,6LAAC;wEAAG,WAAU;kFAA4C,SAAS,IAAI;;;;;;kFACvE,6LAAC;wEAAE,WAAU;kFAAyB,SAAS,MAAM;;;;;;;;;;;;;;;;;;kEAGzD,6LAAC;wDAAO,WAAU;kEAChB,cAAA,6LAAC,kQAAoB;4DAAC,WAAU;;;;;;;;;;;;;;;;;0DAIpC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAAwB;;;;;;0EACxC,6LAAC;gEAAK,WAAW,AAAC,8CAA6E,OAAhC,eAAe,SAAS,MAAM;0EAC1F,cAAc,SAAS,MAAM;;;;;;;;;;;;kEAIlC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAAwB;;;;;;0EACxC,6LAAC;gEAAK,WAAU;0EAAqC,SAAS,eAAe;;;;;;;;;;;;kEAG/E,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAAwB;;;;;;0EACxC,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,8NAAQ;wEAAC,WAAU;;;;;;kFACpB,6LAAC;wEAAK,WAAU;kFAAqC,SAAS,YAAY;;;;;;;;;;;;;;;;;;kEAI9E,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAAwB;;;;;;0EACxC,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,sPAAgB;wEAAC,WAAU;;;;;;kFAC5B,6LAAC;wEAAK,WAAU;kFACb,IAAI,KAAK,SAAS,UAAU,EAAE,kBAAkB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;0DAM1D,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAO,WAAU;8DAAuG;;;;;;;;;;;;;;;;;mCAlDrH,SAAS,EAAE;;;;;;;;;;wBA2DxB,kBAAkB,MAAM,KAAK,mBAC5B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oOAAU;oCAAC,WAAU;;;;;;8CACtB,6LAAC;oCAAG,WAAU;8CAAyC;;;;;;8CACvD,6LAAC;oCAAE,WAAU;8CACV,aAAa,4BAA4B;;;;;;gCAE3C,CAAC,4BACA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCACC,SAAS,IAAM,mBAAmB;wCAClC,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;gBAUV,iCACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA2C;;;;;;0CAEzD,6LAAC;gCAAK,UAAU;gCAAsB,WAAU;;kDAC9C,6LAAC;;0DACC,6LAAC;gDAAM,SAAQ;gDAAO,WAAU;0DAA+C;;;;;;0DAG/E,6LAAC;gDACC,IAAG;gDACH,MAAK;gDACL,OAAO,YAAY,IAAI;gDACvB,UAAU,CAAC,IAAM,eAAe;wDAAE,GAAG,WAAW;wDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;oDAAC;gDACvE,QAAQ;gDACR,WAAU;gDACV,aAAY;;;;;;;;;;;;kDAIhB,6LAAC;;0DACC,6LAAC;gDAAM,SAAQ;gDAAS,WAAU;0DAA+C;;;;;;0DAGjF,6LAAC;gDACC,IAAG;gDACH,MAAK;gDACL,OAAO,YAAY,MAAM;gDACzB,UAAU,CAAC,IAAM,eAAe;wDAAE,GAAG,WAAW;wDAAE,QAAQ,EAAE,MAAM,CAAC,KAAK;oDAAC;gDACzE,QAAQ;gDACR,WAAU;gDACV,aAAY;;;;;;;;;;;;kDAIhB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,MAAK;gDACL,SAAS,IAAM,mBAAmB;gDAClC,WAAU;0DACX;;;;;;0DAGD,6LAAC;gDACC,MAAK;gDACL,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWnB;GA7NwB;KAAA", "debugId": null}}]}