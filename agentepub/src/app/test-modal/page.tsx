'use client'

import { useState } from 'react'
import AuthModal from '@/components/auth/AuthModal'
import SimpleAuthModal from '@/components/auth/SimpleAuthModal'

export default function TestModalPage() {
  const [isOpen, setIsOpen] = useState(false)
  const [isSimpleOpen, setIsSimpleOpen] = useState(false)

  return (
    <div className="min-h-screen bg-gray-100 flex items-center justify-center">
      <div className="bg-white p-8 rounded-lg shadow-lg">
        <h1 className="text-2xl font-bold mb-6">Teste dos Modais de Autenticação</h1>

        <div className="space-y-4">
          <button
            onClick={() => setIsOpen(true)}
            className="w-full bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
          >
            Abrir Modal Original (Headless UI)
          </button>

          <button
            onClick={() => setIsSimpleOpen(true)}
            className="w-full bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700"
          >
            Abrir <PERSON>dal Simples (HTML Puro)
          </button>
        </div>

        <AuthModal
          isOpen={isOpen}
          onClose={() => setIsOpen(false)}
        />

        <SimpleAuthModal
          isOpen={isSimpleOpen}
          onClose={() => setIsSimpleOpen(false)}
        />
      </div>
    </div>
  )
}
