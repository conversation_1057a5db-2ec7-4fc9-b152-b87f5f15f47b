'use client'

import { useState } from 'react'
import AuthModal from '@/components/auth/AuthModal'
import SimpleAuthModal from '@/components/auth/SimpleAuthModal'

export default function TestModalPage() {
  const [isOpen, setIsOpen] = useState(false)
  const [isSimpleOpen, setIsSimpleOpen] = useState(false)

  const testSignUp = () => {
    setIsSimpleOpen(true)
    // Pre-fill with test data after modal opens
    setTimeout(() => {
      const emailInput = document.getElementById('email') as HTMLInputElement
      const passwordInput = document.getElementById('password') as HTMLInputElement
      const nameInput = document.getElementById('fullName') as HTMLInputElement

      if (emailInput) emailInput.value = '<EMAIL>'
      if (passwordInput) passwordInput.value = '123456'
      if (nameInput) nameInput.value = 'Usuário Teste'
    }, 100)
  }

  return (
    <div className="min-h-screen bg-gray-100 flex items-center justify-center">
      <div className="bg-white p-8 rounded-lg shadow-lg max-w-md w-full">
        <h1 className="text-2xl font-bold mb-6">Teste dos Modais de Autenticação</h1>

        <div className="space-y-4">
          <button
            onClick={() => setIsOpen(true)}
            className="w-full bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
          >
            Abrir Modal Original (Headless UI)
          </button>

          <button
            onClick={() => setIsSimpleOpen(true)}
            className="w-full bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700"
          >
            Abrir Modal Simples (HTML Puro)
          </button>

          <button
            onClick={testSignUp}
            className="w-full bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700"
          >
            🧪 Teste Rápido (Cadastro)
          </button>
        </div>

        <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-md">
          <h3 className="font-medium text-yellow-800 mb-2">Instruções:</h3>
          <ul className="text-sm text-yellow-700 space-y-1">
            <li>• Use o botão "Teste Rápido" para cadastro automático</li>
            <li>• Ou crie manualmente: <EMAIL> / 123456</li>
            <li>• Abra o Console do navegador (F12) para ver logs</li>
          </ul>
        </div>

        <AuthModal
          isOpen={isOpen}
          onClose={() => setIsOpen(false)}
        />

        <SimpleAuthModal
          isOpen={isSimpleOpen}
          onClose={() => setIsSimpleOpen(false)}
        />
      </div>
    </div>
  )
}
