'use client'

import { useState } from 'react'
import { useAuth } from '@/contexts/AuthContext'

interface SimpleAuthModalProps {
  isOpen: boolean
  onClose: () => void
  defaultMode?: 'login' | 'signup'
}

export default function SimpleAuthModal({ isOpen, onClose, defaultMode = 'login' }: SimpleAuthModalProps) {
  const [mode, setMode] = useState<'login' | 'signup'>(defaultMode)
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [fullName, setFullName] = useState('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')

  const { signIn, signUp } = useAuth()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError('')

    console.log('🚀 Form submitted:', { mode, email, hasPassword: !!password, hasFullName: !!fullName })

    try {
      let result
      if (mode === 'login') {
        console.log('🔐 Calling signIn...')
        result = await signIn(email, password)
      } else {
        console.log('📝 Calling signUp...')
        result = await signUp(email, password, fullName)
      }

      console.log('📋 Auth result:', result)

      if (result.error) {
        console.error('❌ Auth error:', result.error)
        setError(result.error.message || 'Ocorreu um erro')
      } else {
        console.log('✅ Auth successful, closing modal')
        onClose()
        // Reset form
        setEmail('')
        setPassword('')
        setFullName('')
      }
    } catch (err) {
      console.error('❌ Exception in handleSubmit:', err)
      setError('Ocorreu um erro inesperado: ' + (err instanceof Error ? err.message : String(err)))
    } finally {
      console.log('🏁 Setting loading to false')
      setLoading(false)
    }
  }

  const resetForm = () => {
    setEmail('')
    setPassword('')
    setFullName('')
    setError('')
  }

  const switchMode = () => {
    setMode(mode === 'login' ? 'signup' : 'login')
    resetForm()
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg max-w-md w-full p-6 relative">
        {/* Close button */}
        <button
          onClick={onClose}
          className="absolute top-4 right-4 text-gray-400 hover:text-gray-600 text-2xl"
        >
          ×
        </button>

        {/* Title */}
        <h2 className="text-xl font-bold text-gray-900 mb-6">
          {mode === 'login' ? 'Entrar' : 'Criar Conta'}
        </h2>

        {/* Form */}
        <form onSubmit={handleSubmit} className="space-y-4">
          {error && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md text-sm">
              {error}
            </div>
          )}

          {mode === 'signup' && (
            <div>
              <label htmlFor="fullName" className="block text-sm font-medium text-gray-700 mb-1">
                Nome Completo
              </label>
              <input
                id="fullName"
                type="text"
                value={fullName}
                onChange={(e) => setFullName(e.target.value)}
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Seu nome completo"
              />
            </div>
          )}

          <div>
            <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
              E-mail
            </label>
            <input
              id="email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="<EMAIL>"
            />
          </div>

          <div>
            <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
              Senha
            </label>
            <input
              id="password"
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
              minLength={6}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Mínimo 6 caracteres"
            />
          </div>

          {/* SUBMIT BUTTON - DESTACADO */}
          <button
            type="submit"
            disabled={loading}
            className="w-full bg-blue-600 text-white py-3 px-4 rounded-md font-semibold text-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors border-2 border-blue-600"
            style={{ minHeight: '48px' }}
          >
            {loading ? 'Carregando...' : mode === 'login' ? '🚀 ENTRAR' : '✨ CRIAR CONTA'}
          </button>

          <div className="text-center pt-4">
            <button
              type="button"
              onClick={switchMode}
              className="text-sm text-blue-600 hover:text-blue-700 transition-colors underline"
            >
              {mode === 'login'
                ? 'Não tem conta? Criar uma agora'
                : 'Já tem conta? Fazer login'
              }
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}
