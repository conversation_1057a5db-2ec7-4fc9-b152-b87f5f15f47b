'use client'

import { useState } from 'react'
import { useAuth } from '@/contexts/AuthContext'

interface TestAuthModalProps {
  isOpen: boolean
  onClose: () => void
}

export default function TestAuthModal({ isOpen, onClose }: TestAuthModalProps) {
  const [mode, setMode] = useState<'login' | 'signup'>('signup')
  const [email, setEmail] = useState('<EMAIL>')
  const [password, setPassword] = useState('123456')
  const [fullName, setFullName] = useState('Usuário Teste AgentePub')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')

  const { signIn, signUp } = useAuth()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError('')

    console.log('🚀 TEST MODAL - Form submitted:', { 
      mode, 
      email, 
      password: password ? '***' : 'empty',
      fullName,
      hasPassword: !!password,
      hasFullName: !!fullName 
    })

    try {
      let result
      if (mode === 'login') {
        console.log('🔐 TEST MODAL - Calling signIn...')
        result = await signIn(email, password)
      } else {
        console.log('📝 TEST MODAL - Calling signUp...')
        result = await signUp(email, password, fullName)
      }

      console.log('📋 TEST MODAL - Auth result:', result)

      if (result.error) {
        console.error('❌ TEST MODAL - Auth error:', result.error)
        setError(result.error.message || 'Ocorreu um erro')
      } else {
        console.log('✅ TEST MODAL - Auth successful, closing modal')
        onClose()
      }
    } catch (err) {
      console.error('❌ TEST MODAL - Exception in handleSubmit:', err)
      setError('Ocorreu um erro inesperado: ' + (err instanceof Error ? err.message : String(err)))
    } finally {
      console.log('🏁 TEST MODAL - Setting loading to false')
      setLoading(false)
    }
  }

  const switchMode = () => {
    setMode(mode === 'login' ? 'signup' : 'login')
    setError('')
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg max-w-md w-full p-6 relative">
        {/* Close button */}
        <button
          onClick={onClose}
          className="absolute top-4 right-4 text-gray-400 hover:text-gray-600 text-2xl"
        >
          ×
        </button>

        {/* Title */}
        <h2 className="text-xl font-bold text-gray-900 mb-6">
          🧪 TESTE - {mode === 'login' ? 'Entrar' : 'Criar Conta'}
        </h2>

        {/* Form */}
        <form onSubmit={handleSubmit} className="space-y-4">
          {error && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md text-sm">
              {error}
            </div>
          )}

          {mode === 'signup' && (
            <div>
              <label htmlFor="test-fullName" className="block text-sm font-medium text-gray-700 mb-1">
                Nome Completo
              </label>
              <input
                id="test-fullName"
                type="text"
                value={fullName}
                onChange={(e) => setFullName(e.target.value)}
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Seu nome completo"
              />
              <div className="text-xs text-gray-500 mt-1">Valor: "{fullName}"</div>
            </div>
          )}

          <div>
            <label htmlFor="test-email" className="block text-sm font-medium text-gray-700 mb-1">
              E-mail
            </label>
            <input
              id="test-email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="<EMAIL>"
            />
            <div className="text-xs text-gray-500 mt-1">Valor: "{email}"</div>
          </div>

          <div>
            <label htmlFor="test-password" className="block text-sm font-medium text-gray-700 mb-1">
              Senha
            </label>
            <input
              id="test-password"
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
              minLength={6}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Mínimo 6 caracteres"
            />
            <div className="text-xs text-gray-500 mt-1">Valor: "{password}"</div>
          </div>

          {/* SUBMIT BUTTON */}
          <button
            type="submit"
            disabled={loading}
            className="w-full bg-purple-600 text-white py-3 px-4 rounded-md font-semibold text-lg hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors border-2 border-purple-600"
            style={{ minHeight: '48px' }}
          >
            {loading ? '⏳ CARREGANDO...' : mode === 'login' ? '🔐 ENTRAR TESTE' : '🧪 CRIAR CONTA TESTE'}
          </button>

          <div className="text-center pt-4">
            <button
              type="button"
              onClick={switchMode}
              className="text-sm text-purple-600 hover:text-purple-700 transition-colors underline"
            >
              {mode === 'login' 
                ? 'Não tem conta? Criar uma agora' 
                : 'Já tem conta? Fazer login'
              }
            </button>
          </div>
        </form>

        {/* Debug info */}
        <div className="mt-4 p-3 bg-gray-50 rounded text-xs">
          <strong>Debug:</strong><br/>
          Mode: {mode}<br/>
          Email: {email}<br/>
          Password: {password ? '***' : 'empty'}<br/>
          Name: {fullName}<br/>
          Loading: {loading ? 'true' : 'false'}
        </div>
      </div>
    </div>
  )
}
