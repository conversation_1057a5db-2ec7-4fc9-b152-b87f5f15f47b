'use client'

import { createContext, useContext, useEffect, useState } from 'react'
import { User, Session } from '@supabase/supabase-js'
import { supabase } from '@/lib/supabase'
import { Database } from '@/lib/supabase'

type Profile = Database['public']['Tables']['profiles']['Row']

interface AuthContextType {
  user: User | null
  profile: Profile | null
  session: Session | null
  loading: boolean
  signUp: (email: string, password: string, fullName: string) => Promise<{ error: any }>
  signIn: (email: string, password: string) => Promise<{ error: any }>
  signOut: () => Promise<void>
  updateProfile: (updates: Partial<Profile>) => Promise<{ error: any }>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [profile, setProfile] = useState<Profile | null>(null)
  const [session, setSession] = useState<Session | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Get initial session
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session)
      setUser(session?.user ?? null)
      if (session?.user) {
        fetchProfile(session.user.id)
      } else {
        setLoading(false)
      }
    })

    // Listen for auth changes
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event, session) => {
      setSession(session)
      setUser(session?.user ?? null)

      if (session?.user) {
        await fetchProfile(session.user.id)
      } else {
        setProfile(null)
        setLoading(false)
      }
    })

    return () => subscription.unsubscribe()
  }, [])

  const fetchProfile = async (userId: string) => {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single()

      if (error) {
        // If profile doesn't exist (PGRST116) or table doesn't exist (42P01)
        if (error.code === 'PGRST116' || error.code === '42P01') {
          console.log('Profile not found or table does not exist. User can still use the app.')
          setProfile(null)
        } else {
          console.warn('Error fetching profile (non-critical):', error.message)
          setProfile(null)
        }
      } else {
        setProfile(data)
      }
    } catch (error) {
      console.warn('Error fetching profile (non-critical):', error)
      setProfile(null)
    } finally {
      setLoading(false)
    }
  }

  const signUp = async (email: string, password: string, fullName: string) => {
    try {
      console.log('📝 Attempting to sign up with:', email, fullName)

      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            full_name: fullName,
          },
        },
      })

      if (error) {
        console.error('❌ Sign up error:', error)
        return { error }
      }

      console.log('✅ Sign up successful:', data.user?.email)

      // If signup successful and user is confirmed, create profile manually
      if (!error && data.user && !data.user.email_confirmed_at) {
        // For email confirmation flow, profile will be created when user confirms
        console.log('📧 User signed up, waiting for email confirmation')
      } else if (!error && data.user && data.user.email_confirmed_at) {
        // User is immediately confirmed, create profile
        console.log('👤 Creating profile for confirmed user')
        await createProfileIfNotExists(data.user.id, email, fullName)
      }

      return { error: null }
    } catch (error) {
      console.error('❌ Sign up exception:', error)
      return { error }
    }
  }

  const createProfileIfNotExists = async (userId: string, email: string, fullName: string) => {
    try {
      const { error } = await supabase
        .from('profiles')
        .insert({
          id: userId,
          email: email,
          full_name: fullName
        })

      if (error && error.code !== '23505') { // 23505 is unique violation (profile already exists)
        console.error('Error creating profile:', error)
      }
    } catch (error) {
      console.error('Error creating profile:', error)
    }
  }

  const signIn = async (email: string, password: string) => {
    try {
      console.log('🔐 Attempting to sign in with:', email)

      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      })

      if (error) {
        console.error('❌ Sign in error:', error)
        return { error }
      }

      console.log('✅ Sign in successful:', data.user?.email)
      return { error: null }
    } catch (error) {
      console.error('❌ Sign in exception:', error)
      return { error }
    }
  }

  const signOut = async () => {
    await supabase.auth.signOut()
  }

  const updateProfile = async (updates: Partial<Profile>) => {
    if (!user) return { error: 'No user logged in' }

    try {
      const { data, error } = await supabase
        .from('profiles')
        .update(updates)
        .eq('id', user.id)
        .select()
        .single()

      if (error) {
        return { error }
      }

      setProfile(data)
      return { error: null }
    } catch (error) {
      return { error }
    }
  }

  const value = {
    user,
    profile,
    session,
    loading,
    signUp,
    signIn,
    signOut,
    updateProfile,
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
