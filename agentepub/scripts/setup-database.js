const { createClient } = require('@supabase/supabase-js')
require('dotenv').config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables')
  console.log('Make sure you have NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY in your .env.local file')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function setupDatabase() {
  console.log('🚀 Setting up AgentePub database...')

  try {
    // Test connection
    const { data: testData, error: testError } = await supabase
      .from('profiles')
      .select('count')
      .limit(1)

    if (testError && testError.code === '42P01') {
      console.log('📋 Tables not found. You need to run the SQL migrations manually.')
      console.log('\n🔧 Please follow these steps:')
      console.log('1. Go to https://supabase.com/dashboard')
      console.log('2. Select your project')
      console.log('3. Go to SQL Editor')
      console.log('4. Run the SQL from: supabase/migrations/001_initial_schema.sql')
      console.log('5. Run the SQL from: supabase/migrations/002_rls_policies.sql')
      console.log('6. Run the SQL from: supabase/migrations/003_functions_triggers.sql')
      console.log('\n📖 Or copy and paste this minimal setup:')
      console.log(`
-- Minimal setup for AgentePub
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create custom types
CREATE TYPE user_area AS ENUM ('atendimento', 'planejamento', 'midia');

-- Create profiles table
CREATE TABLE profiles (
    id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    email VARCHAR(255) NOT NULL UNIQUE,
    full_name VARCHAR(255),
    area user_area,
    team_id UUID,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Users can view their own profile" ON profiles
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update their own profile" ON profiles
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert their own profile" ON profiles
    FOR INSERT WITH CHECK (auth.uid() = id);

-- Auto-create profile trigger
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.profiles (id, email, full_name)
    VALUES (NEW.id, NEW.email, NEW.raw_user_meta_data->>'full_name');
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();
      `)
      return
    }

    if (testError) {
      console.error('❌ Database connection error:', testError.message)
      return
    }

    console.log('✅ Database connection successful!')
    console.log('✅ Tables are set up correctly!')
    
    // Check if we have any users without profiles
    const { data: users } = await supabase.auth.admin.listUsers()
    const { data: profiles } = await supabase.from('profiles').select('id')
    
    const userIds = users.users.map(u => u.id)
    const profileIds = profiles.map(p => p.id)
    const usersWithoutProfiles = userIds.filter(id => !profileIds.includes(id))
    
    if (usersWithoutProfiles.length > 0) {
      console.log(`🔧 Found ${usersWithoutProfiles.length} users without profiles. Creating them...`)
      
      for (const userId of usersWithoutProfiles) {
        const user = users.users.find(u => u.id === userId)
        const { error } = await supabase
          .from('profiles')
          .insert({
            id: userId,
            email: user.email,
            full_name: user.user_metadata?.full_name || null
          })
        
        if (error) {
          console.error(`❌ Error creating profile for ${user.email}:`, error.message)
        } else {
          console.log(`✅ Created profile for ${user.email}`)
        }
      }
    }
    
    console.log('🎉 Database setup complete!')
    
  } catch (error) {
    console.error('❌ Setup failed:', error.message)
  }
}

setupDatabase()
