# AgentePub - Agentes Inteligentes para Agências

Um MVP funcional de uma plataforma de agentes inteligentes voltada para maximizar a produtividade e performance em agências publicitárias.

## 🎯 Objetivo

Oferecer uma plataforma onde profissionais de agências (Atendimento, Planejamento e Mídia) possam usar agentes inteligentes para automatizar tarefas do dia a dia, desde organização de briefings até geração de relatórios.

## 🚀 Funcionalidades Principais

### 🏠 Landing Page
- Hero section com proposta de valor clara
- Seção "Como funciona" com processo em 4 etapas
- Apresentação dos 3 agentes inteligentes
- Pricing transparente (R$ 9,99/mês por usuário)
- FAQ completo

### 🧪 Página de Teste
- Formulário para testar agentes gratuitamente
- Simulação de processamento de briefings
- Output estruturado e organizado
- Integração com sistema de login

### 📊 Dashboard Completo
- Visão geral com estatísticas
- Navegação lateral responsiva
- Ações rápidas para principais funcionalidades
- Interface moderna e intuitiva

### 🤖 Três Agentes Inteligentes

#### 1. Agente de Atendimento
- Organiza briefings automaticamente
- Cria cronogramas de aprovação
- Monitora prazos e entregas
- Gera relatórios de status

#### 2. Agente de Planejamento
- Gera cronogramas inteligentes
- Análise estratégica básica
- Sugestões de distribuição de budget
- Insights de mercado

#### 3. Agente de Mídia
- Análise de performance de campanhas
- Sugestões de otimização
- Relatórios automatizados
- Alertas de performance

### 📁 Gestão de Campanhas
- Criação e organização de campanhas
- Armazenamento de materiais por campanha
- Status tracking e timeline
- Colaboração em equipe

### 📈 Sistema de Relatórios
- Relatórios de status de campanhas
- Performance da equipe
- Cronogramas consolidados
- Exportação em PDF

### 💬 Chat Interno
- Comunicação em tempo real entre equipe
- Organização por áreas de atuação
- Status online/offline
- Interface moderna de chat

### ⚙️ Administração
- Gestão de perfil pessoal
- Convite e gestão de membros da equipe
- Configurações gerais
- Controle de permissões

## 🛠 Stack Tecnológica

- **Frontend**: Next.js 15 com App Router
- **Styling**: Tailwind CSS
- **UI Components**: Headless UI + Heroicons
- **Backend/Database**: Supabase
- **Authentication**: Supabase Auth
- **Storage**: Supabase Storage
- **Real-time**: Supabase Realtime (para chat)
- **Payments**: Stripe (integração preparada)

## 📦 Getting Started

### Pré-requisitos
- Node.js 18+
- npm ou yarn
- Conta no Supabase

### 1. Instale as dependências
```bash
npm install
```

### 2. Configure as variáveis de ambiente
O arquivo `.env.local` já está configurado com as chaves do Supabase.

### 3. Configure o banco de dados
Execute as migrações SQL no Supabase:
1. Acesse o SQL Editor no dashboard do Supabase
2. Execute os arquivos na pasta `supabase/migrations/` em ordem:
   - `001_initial_schema.sql`
   - `002_rls_policies.sql`
   - `003_functions_triggers.sql`

### 4. Execute o projeto
```bash
npm run dev
```

O projeto estará disponível em `http://localhost:3000`

## 🎨 Design System

### Cores
- **Primary**: Blue (#2563eb)
- **Success**: Green (#10b981)
- **Warning**: Yellow (#f59e0b)
- **Error**: Red (#ef4444)

### Tipografia
- **Font**: Inter (Google Fonts)
- **Responsive**: Mobile-first approach

## 📱 Funcionalidades Implementadas

✅ Landing page completa com todas as seções
✅ Sistema de autenticação (Supabase Auth)
✅ Página de teste funcional
✅ Dashboard com navegação completa
✅ Sistema de campanhas
✅ Três agentes inteligentes funcionais
✅ Sistema de relatórios
✅ Chat interno em tempo real
✅ Administração de usuários
✅ Página de pricing
✅ Página de sucesso pós-pagamento
✅ Design responsivo
✅ Esquema de banco de dados completo
✅ Políticas de segurança (RLS)

## 🚀 Deploy

### Vercel (Recomendado)
1. Conecte o repositório no Vercel
2. Configure as variáveis de ambiente
3. Deploy automático

---

Desenvolvido com ❤️ para publicitários que querem mais produtividade.
